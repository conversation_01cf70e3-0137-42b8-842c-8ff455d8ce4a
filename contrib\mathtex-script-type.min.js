!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("katex"));else if("function"==typeof define&&define.amd)define(["katex"],t);else{var n="object"==typeof exports?t(require("katex")):t(e.katex);for(var r in n)("object"==typeof exports?exports:e)[r]=n[r]}}("undefined"!=typeof self?self:this,(function(e){return function(){"use strict";var t={771:function(t){t.exports=e}},n={};function r(e){var o=n[e];if(void 0!==o)return o.exports;var i=n[e]={exports:{}};return t[e](i,i.exports,r),i.exports}r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,{a:t}),t},r.d=function(e,t){for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)};var o={};return function(){var e=r(771),t=r.n(e);let n=document.body.getElementsByTagName("script");n=Array.prototype.slice.call(n),n.forEach((function(e){if(!e.type||!e.type.match(/math\/tex/i))return-1;const n=null!=e.type.match(/mode\s*=\s*display(;|\s|\n|$)/),r=document.createElement(n?"div":"span");r.setAttribute("class",n?"equation":"inline-equation");try{t().render(e.text,r,{displayMode:n})}catch(t){r.textContent=e.text}e.parentNode.replaceChild(r,e)}))}(),o=o.default}()}));