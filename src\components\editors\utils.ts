// 检查文件大小是否超过限制
export const isFileSizeExceeded = (
  file: File,
  maxSize: number = 2 * 1024 * 1024
): boolean => {
  const result = file.size > maxSize;
  if (result) {
    console.log(
      `文件大小检查: ${file.name}, 大小: ${file.size}字节, 超过2MB限制`
    );
  }
  return result;
};
export const safeFilename = (name: string): string => {
  return (
    name
      // 只保留英文字母、数字、中文和点号
      .replace(/[^(a-zA-Z0-9\u4e00-\u9fa5\.)]/g, "")
      // 移除特殊字符(?/\:|<>*[]()${}@~)
      .replace(/[\?\\/:|<>\*\[\]\(\)\$%\{\}@~]/g, "")
      // 移除空白字符
      .replace("/\\s/g", "")
  );
};

/**
 * 将<script type="math/tex; mode=display">公式</script>和<script type="math/tex">公式</script>逆向转换为正确的LaTeX格式
 * @param text - 包含math脚本标签的文本
 * @returns 转换后的文本
 */
export function revertMathScriptsToMd(text: string): string {
  if (!text) return '';

  // 块级公式转为$$公式$$
  const displayRegex = /<script\s+type="math\/tex;\s*mode=display">(.*?)<\/script>/gs;
  let result = text.replace(displayRegex, (_match, formula) => {
    return `$$${formula}$$`;
  });

  // 行内公式转为$公式$
  const inlineRegex = /<script\s+type="math\/tex">(.*?)<\/script>/gs;
  result = result.replace(inlineRegex, (_match, formula) => {
    return `$${formula}$`;
  });
  return result;
}
// 处理LaTeX公式转义问题的辅助函数
export const unescapeLatexFormulas = (text: string): string => {
  // 处理各种LaTeX公式转义情况
  let result = text;
  // 情况1: 完全转义的公式 \$x^2\$ -> $x^2$
  result = result.replace(/\\\$([^$]*)\\\$/g, '$$$1$');
  // 情况2: 只有结尾被转义的公式 $x^2\$ -> $x^2$
  result = result.replace(/(\$[^$]*)\\\$/g, '$1$');
  // 情况3: 只有开头被转义的公式 \$x^2$ -> $x^2$
  result = result.replace(/\\\$([^$]*\$)/g, '$$$1');
  return result;
};

