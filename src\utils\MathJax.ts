import _ from 'lodash';
const TypeSet = async function () {
  if (!window.MathJax) {
    return;
  }
  window.MathJax.startup.promise = window.MathJax.startup.promise
    .then(() => {
      return window.MathJax.typesetPromise();
    })
    .catch((err) => console.log('Typeset failed: ' + err.message));
  return window.MathJax.startup.promise;
};
const debouceTypeSet = _.debounce(TypeSet, 5000);
const throttleTypeSet = _.throttle(TypeSet, 5000);

let numbers = -1;
const myTypeSet = () => {
  const nodeCount = document.querySelectorAll('script[type^="math/tex"]').length;
  if (numbers != nodeCount) {
    numbers = nodeCount;
    TypeSet();
  }
};

const MathQueue = function (elClassName?: string) {
  if (!window.MathJax) {
    return;
  }

  window.MathJax.Hub.Queue([
    'Typeset',
    window.MathJax.Hub,
    document.getElementsByClassName(elClassName)
  ]);
};

const MathQueueByElement = function (el: HTMLElement) {
  if (!window.MathJax) {
    return;
  }
  window.MathJax.Hub.Queue(['Typeset', window.MathJax.Hub, el]);
};

export default {
  TypeSet,
  debouceTypeSet,
  throttleTypeSet,
  myTypeSet,
  MathQueue,
  MathQueueByElement
};
