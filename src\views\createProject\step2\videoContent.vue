<script setup lang="ts">
import VideoUploader from "@/views/createProject/step2/videoUploader.vue";
import VideoLecture from "@/views/createProject/step2/videoLecture.vue";
import { reactive, watch, ref, inject, type Ref, provide } from "vue";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import type {
  videoContentType,
  lectureType,
  videoSectionType,
} from "@/utils/type";
import { checkCoverUrl, checkVideoUrl } from "@/utils/func";
import ImgUpload from "@/views/createProject/components/imgUpload.vue";
import {
  getVideoSection,
  type params4submitVideoSection,
  submitVideoSection,
} from "@/apis/path/createProject";

// 本组件进行前后端通信
const emits = defineEmits([
  "sendSecName",
  "saveSectionDraftDone",
  "saveSectionDone",
]);
const props = defineProps({
  projectId: Number,
  sectionId: Number,
  releaseDate: {
    type: String,
    required: true,
  },
  sectionCount: Number,
});
const rules = reactive<FormRules<videoSectionType>>({
  sectionName: [{ required: true, message: "请填写小节标题", trigger: "blur" }],
  videoFlag: [{ required: true, message: "请上传视频", trigger: "blur" }],
  // videoCover: [{ required: true, validator: checkCoverUrl, trigger: 'blur' }],
  videoKey: [{ required: true, message: "请上传视频", trigger: "blur" }],
  lectureFlag: [{ required: true, message: "请上传视频讲稿", trigger: "blur" }],
});
const prjId = ref();
const secId = ref();
// const secName = ref();
const rlsDate = ref();
const secCount = ref();
const ruleFormRef = ref<FormInstance>();
const formData = reactive<videoSectionType>({
  sectionName: "",
  videoFlag: "",
  lectureFlag: "",
  videoCover: {
    echoUrl: "",
    commUrl: "",
  },
  videoKey: "",
  lecture: [
    {
      beginning: false,
      id: 0,
      time: "",
      content: "",
    },
  ],
});
// const uploadingRef = ref();
// provide('uploading', uploadingRef);
const initData = () => {
  if (secId.value) {
    getVideoSection(secId.value)
      .then((res) => {
        if (res.success) {
          formData.videoCover.echoUrl = res.data.list[0].longImageUrl;
          formData.videoCover.commUrl = res.data.list[0].shortImageUrl;
          formData.videoKey = res.data.list[0].longVideoUrl;
          formData.videoFlag = res.data.list[0].longVideoUrl;
          formData.lecture = res.data.list[0].speechText;
          formData.lectureFlag =
            res.data.list[0].speechText.length === 0 ? "" : "true";
          formData.sectionName = res.data.list[0].sectionTitle
            ? res.data.list[0].sectionTitle
            : "默认标题";
          // console.log('getLecture: ' + JSON.stringify(formData.lecture, null, 2));
          emits(
            "sendSecName",
            formData.sectionName ? formData.sectionName : "默认标题"
          );
        } else {
          ElMessage.error(res.message);
        }
      })
      .catch();
  }
};
watch(
  () => props,
  (newValue, oldValue) => {
    prjId.value = newValue.projectId;
    secId.value = newValue.sectionId;
    secCount.value = newValue.sectionCount;
    rlsDate.value = newValue.releaseDate;
    // console.log("rlsDate.value", rlsDate.value);
    if (secId.value == -1) {
      return;
    }
    initData();
  },
  { deep: true, immediate: true }
);
// const initVideoContent = (projectId: number, sectionId: number, releaseDate: string) => {
//   prjId.value = projectId;
//   secId.value = sectionId;
//   rlsDate.value = releaseDate;
//   console.log('initVideoContent in singleComp: prjId = ' + prjId.value + ' secId = ' + secId.value + ' rlsDate = ' + rlsDate.value + '');
// }
const receiveVideoKey = (videoKey: string) => {
  // formData.videoKey = videoKey;
  formData.videoFlag = videoKey;
  // console.log('receiveVideoKey: ' + videoKey);
};
const receiveCover = (CommUrl: string, EchoUrl: string) => {
  // commUrl: 前后端通信时的图片链接(short)
  // echoUrl: 前端展示的图片链接(long)
  const newCover = {
    echoUrl: EchoUrl,
    commUrl: CommUrl,
  };
  formData.videoCover = newCover;
};
const receiveLecture = (lectureList: lectureType[]) => {
  formData.lecture = lectureList;
  formData.lectureFlag = "true";
};
const setSectionName = (newSecName: string) => {
  formData.sectionName = newSecName;
};
const handleSaveDraftSection = () => {
  if (secId.value == undefined || secCount.value == undefined) return;
  const param: params4submitVideoSection = {
    chapterNum: secCount.value,
    sectionId: secId.value,
    sectionTitle: formData.sectionName,
  };
  submitVideoSection(param)
    .then((res) => {
      if (res.success) {
        // ElMessage.success(res.message);
        // 表明本次保存草稿结束，重置父组件中的saveSectionId
        emits("saveSectionDraftDone", true);
      } else {
        ElMessage.error(res.message);
        emits("saveSectionDraftDone", false);
      }
    })
    .catch();
};
const handleSaveSection = () => {
  if (secId.value == undefined || secCount.value == undefined) return;
  if (!ruleFormRef.value) {
    emits("saveSectionDone", false);
    return;
  }
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      console.log("表单校验合法");
      const param: params4submitVideoSection = {
        chapterNum: secCount.value,
        sectionId: secId.value,
        sectionTitle: formData.sectionName,
      };
      submitVideoSection(param)
        .then((res) => {
          if (res.success) {
            // ElMessage.success(res.message);
            // 表明本次校验保存结束，父组件中应该跳转到下一步
            emits("saveSectionDone", true);
          } else {
            ElMessage.error(res.message);
            emits("saveSectionDone", false);
          }
        })
        .catch();
    } else {
      console.log("表单校验失败");
      emits("saveSectionDone", false);
    }
  });
};
const handleCleanLecture = (success: boolean) => {
  if (success) {
    formData.lecture = [];
    formData.lectureFlag = "";
  }
};

const saveDraftSectionId = inject("saveDraftSection") as Ref;
const saveSectionId = inject("saveSection") as Ref;
watch(
  () => saveDraftSectionId,
  (newVal, oldVal) => {
    if (newVal.value != -1 && secId.value == newVal.value) {
      handleSaveDraftSection();
    }
  },
  { deep: true, immediate: true }
);
watch(
  () => saveSectionId,
  (newVal, oldVal) => {
    if (newVal.value != -1 && secId.value == newVal.value) {
      handleSaveSection();
    }
  },
  { deep: true, immediate: true }
);
defineExpose({
  setSectionName,
});
</script>

<template>
  <div class="main-wrapper">
    <el-form
      class="form"
      ref="ruleFormRef"
      :model="formData"
      :rules="rules"
      @submit.prevent
    >
      <el-form-item class="video-upload" prop="videoFlag">
        <video-uploader
          @sendVideoKey="receiveVideoKey"
          :videoKey="formData.videoKey"
          :project-id="prjId"
          :section-id="secId"
          :releaseDate="rlsDate"
        ></video-uploader>
      </el-form-item>
      <el-form-item label="视频封面" prop="videoCover">
        <div class="cover-content">
          <img-upload
            @send-img-url="receiveCover"
            :PrjId="prjId"
            :SectionId="secId"
            :LongUrl="formData.videoCover.echoUrl"
            :can-remove="true"
          ></img-upload>
          <div class="tip">
            视频封面格式为jpeg,png,文件大小不超过2MB,建议长宽比为4:3 。
          </div>
        </div>
      </el-form-item>
      <el-form-item label="视频文稿" prop="lectureFlag">
        <video-lecture
          @sendLecture="receiveLecture"
          @cleanLecture="handleCleanLecture"
          :lecture="formData.lecture"
          :project-id="prjId"
          :section-id="secId"
          style="padding-left: 10px"
        ></video-lecture>
      </el-form-item>
    </el-form>
  </div>
</template>

<style scoped>
.main-wrapper {
  width: 100%;
  display: flex;
  .form {
    width: 100%;
    .el-form-item:not(.video-upload) {
      align-items: flex-start;
      flex-direction: column;
    }
    :deep(.el-form-item__content) {
      width: 100%;
    }
    .cover-content {
      display: flex;
      flex-direction: column;
      padding-left: 10px;
      .tip {
        font-size: 12px;
        color: var(--color-grey);
      }
    }

    :deep(.el-upload--picture-card) {
      width: 239px;
      height: 180px;
    }
  }
}
</style>
