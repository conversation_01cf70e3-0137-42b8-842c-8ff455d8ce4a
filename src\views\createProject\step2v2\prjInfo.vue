<script setup lang="ts">
import MyTag from "@/views/common/myTag.vue"
import { computed, inject, onMounted, reactive, ref, watch } from "vue"
import type { prjInfoType, tagType } from "@/utils/type"
const props = defineProps({
  infoData: {
    type: Object as () => prjInfoType,
    required: true,
  },
  showTarget: {
    type: Boolean,
    required: false,
    default: false,
  },
})
const prjInfoData = ref<prjInfoType>({
  disable: false,
  prjType: "",
  prjName: "",
  prjAim: "",
  prjGeneral: "",
  prjTagList: [] as tagType[],
  prjTargetList: [] as tagType[],
  prjAreaList: [] as tagType[],
  prjCover: {
    commUrl: "",
    echoUrl: "",
  },
})

const active = computed(() => {
  return "state_" + prjInfoData.value.prjType
})

const curType = ref({
  "": "unreachable",
  "1": "知识讲解",
  "2": "案例学习",
  "3": "知识测评",
  "4": "领域讲解",
})
const handleShow = () => {}
onMounted(() => {
  handleShow()
})
watch(
  () => props.infoData,
  (newValue, oldValue) => {
    prjInfoData.value = { ...newValue }
  },
  { deep: true, immediate: true }
)

const isEllipsis = (data: string) => {
  // 使用正则表达式替换掉所有的 < 和 > 以及它们之间的内容
  const cleanedData = data.replace(/<[^>]*>/g, "")
  return cleanedData.length > maxWidth ? true : false
}

const maxWidth = 5
</script>

<template>
  <div class="info-wrapper">
    <div class="floor">
      <span class="left-wrapper">
        <div class="headFloor">
          <span class="title htmlContent1 ck-content" style="max-width: 800px">
            <template>
              <slot name="prjname"></slot>
            </template>
          </span>
          <span class="typeTip" :class="active">
            <template>
              <slot name="typeTip"></slot>
            </template>
          </span>
        </div>
        <!-- <div class="aim ck-content" v-html="prjInfoData.prjAim"></div> -->
        <!-- <div class="general ck-content" v-html="prjInfoData.prjGeneral"></div> -->
        <div class="aim">
            <template>
              <slot name="aim"></slot>
            </template>
        </div>
        <div class="general">
            <template>
              <slot name="general"></slot>
            </template>
        </div>
        <!-- <div class="tagBar">
        <my-tag class="t" v-for="t in prjInfoData.prjTagList" :tag-id="t.id" :key="t.id" type="tag" :deletable="false">
          <el-tooltip
              popper-class="tooltip-width"
              raw-content
              trigger="click"
          >
            <template #content>
              {{ t.name }}
              <div class="tooltipHtml" v-html="t.name"></div>
            </template>
          <span v-html="t.name"></span>
          </el-tooltip>
        </my-tag>
      </div> -->
      </span>
      <span class="right-wrapper">
        <template>
            <slot name="cover"></slot>
        </template>
        <!-- <img class="miniPic" :src="prjInfoData.prjCover.echoUrl" /> -->
      </span>
    </div>

    <div
      class="targetBar"
      v-if="
        (prjInfoData.prjType === '1' || prjInfoData.prjType === '3') &&
        props.showTarget
      "
    >
      <div class="title">
        {{ prjInfoData.prjType == "1" ? "讲解" : "测评" }}目标
      </div>
      <div class="content">
        <template>
            <slot></slot>
        </template>
        <!-- <my-tag
          class="t"
          v-for="t in prjInfoData.prjTargetList"
          :tag-id="t.id"
          :key="t.id"
          type="target"
          :deletable="false"
          :ellipsis="isEllipsis(t.name)"
        >
          <el-tooltip
            :content="t.name"
            raw-content
            @show="handleShow"
            popper-class="tooltip-width"
          >
            <span class="htmlContent1" v-html="t.name"></span>
          </el-tooltip>
        </my-tag> -->
      </div>
    </div>
  </div>
  <!-- other -->
</template>

<style scoped>
.info-wrapper {
  display: flex;
  flex-direction: column;
  .floor {
    width: 100%;
    display: flex;
    flex-direction: row;
    margin-bottom: 10px;
    .left-wrapper {
      width: 901px;
      flex-direction: column;
      margin-right: 20px;
      /*background-color: #1661AB;*/
      .headFloor {
        display: flex;
        align-items: center;
        flex-direction: row;
        margin-bottom: 20px;
        .title {
          font-size: 24px;
          font-weight: 600;
          margin-right: 10px;
          font-family: var(--font-family-info);
        }
        .typeTip {
          width: 70px;
          height: 24px;
          line-height: 24px;
          display: flex;
          justify-content: center;
          border-radius: 5px;
          border: 1px solid var(--color-grey);
        }

        .state_1 {
          align-items: center;
          border: 1px solid var(--color-case-btn);
          color: var(--color-case-btn);
        }
        .state_2 {
          align-items: center;
          border: 1px solid var(--color-explain-btn);
          color: var(--color-explain-btn);
        }
        .state_3 {
          align-items: center;
          border: 1px solid var(--color-test-btn);
          color: var(--color-test-btn);
        }
        .state_4 {
          align-items: center;
          border: 1px solid var(--color-primary);
          color: var(--color-primary);
        }
      }
      .aim {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 10px;
        font-family: var(--font-family-info);
        word-break: break-all;
      }
      .general {
        font-size: 14px;
        font-family: var(--font-family-text);
        margin-bottom: 30px;
        word-break: break-all;
      }
      .tagBar {
        display: flex;
        align-items: flex-start;
        flex-direction: row;
        flex-wrap: wrap;
      }
    }
    .right-wrapper {
      .miniPic {
        height: 180px;
        width: 239px;
        border-radius: 5px;
      }
    }
    /* &::after {
      content: '';
      height: 1px;
      width: 100%;
      background-color: var(--color-line);
      position: absolute;
      bottom: 0;
    } */
  }
  .targetBar {
    background-color: var(--color-light);
    display: flex;
    flex-direction: column;
    padding: 10px;
    margin-bottom: 20px;
    .title {
      font-size: 16px;
      line-height: 16px;
      font-weight: 600;
      margin-bottom: 10px;
    }
    .content {
      display: flex;
      align-items: flex-start;
      flex-direction: row;
      flex-wrap: wrap;
    }
  }
  .t {
    margin-right: 20px;
    margin-bottom: 10px;
  }
}
</style>
