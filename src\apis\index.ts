import { createAxiosByinterceptors } from "./request"
import type { AxiosInstance } from "axios"

let http: AxiosInstance
let http_auth: AxiosInstance
let http_info: AxiosInstance
;(function () {
  http = createAxiosByinterceptors({
    baseURL: import.meta.env.VITE_APP_PRJ_API_BASE,
  })
  http_auth = createAxiosByinterceptors({
    baseURL: import.meta.env.VITE_APP_AUTH_API_BASE,
  })
  http_info = createAxiosByinterceptors({
    baseURL: import.meta.env.VITE_APP_INFO_API_BASE,
  })
})()

export { http, http_auth, http_info }
