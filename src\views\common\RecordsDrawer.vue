<script setup lang="ts">
import RecordsListBlock from "./RecordsListBlock.vue";
import { ref } from "vue";
import { getMyPrjRecordApi } from "@/apis/path/myProject";
import { renderMarkdown } from "@/utils/markdown";

const drawerVisible = ref(false);
const prjId = ref();
const prjTitle = ref();
const recordsList = ref();

// 展示drawer
const showDrawer = (data: any) => {
  prjId.value = data.id;
  prjTitle.value = data.title;
  getRecordsList(prjId.value);
};

// 获取记录列表
const getRecordsList = (id: number) => {
  getMyPrjRecordApi(id).then((res) => {
    if (res.success) {
      recordsList.value = res.data.list;
      drawerVisible.value = true;
    }
  });
};

defineExpose({
  showDrawer,
});
</script>
<template>
  <el-drawer v-model="drawerVisible" direction="ltr" size="30%">
    <template #header>
      <div class="header-text">审核记录</div>
    </template>
    <span class="title">
      <span class="ck-content" v-html="renderMarkdown(prjTitle)"></span>
    </span>
    <div class="wrapper">
      <RecordsListBlock :list="recordsList"></RecordsListBlock>
    </div>
  </el-drawer>
</template>
<style scoped>
.header-text {
  font-family: var(--font-family-text);
  color: var(--color-black);
  font-weight: 600;
}
.title {
  font-family: var(--font-family-text);
  color: var(--color-black);
  font-weight: 600;
}
.wrapper {
  margin-top: 20px;
  font-size: 14px;
  color: var(--color-black);
  font-family: var(--font-family-text);
}
.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}
</style>
<style>
.el-drawer__header {
  margin: 0;
}
</style>
