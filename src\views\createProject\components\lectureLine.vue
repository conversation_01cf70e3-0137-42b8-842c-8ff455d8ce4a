<script setup lang="ts">
import { ref, watch } from "vue"
import type { lectureType } from "@/utils/type"
import { ElMessage } from "element-plus"
import {
  type params4updateVideoLecture,
  updateVideoLecture,
} from "@/apis/path/createProject"
import { processAllLatexEquations } from '@/utils/latexUtils';

const props = defineProps({
  step: {
    type: Number,
    required: true,
  },
  index: {
    // 如果是第一条，index0，不能取消选中段首
    type: Number,
    required: true,
  },
  lecture: {
    type: Object,
    required: true,
  },
})
const curLecture = ref<lectureType>({
  beginning: false,
  id: 0,
  time: "",
  content: "",
})
const stepMode = ref<number>()
const lectureIndex = ref<number>()
const isEditing = ref(false)
const inputRef = ref()
watch(
  () => props,
  (newVal, oldVal) => {
    curLecture.value = { ...(newVal.lecture as lectureType) }
    lectureIndex.value = newVal.index
    stepMode.value = newVal.step
    // console.log('lecture: ' + JSON.stringify(curLecture.value, null ,2));
  },
  { deep: true, immediate: true }
)
const change2ReadMode = () => {
  if (stepMode.value == 2) {
    isEditing.value = false
  }
}
const change2EditMode = () => {
  if (stepMode.value == 2) {
    isEditing.value = true
    inputRef.value.focus()
  }
}
const handleSaveContent = () => {
  if (curLecture.value.content.length > 1000) {
    ElMessage.error("文本过长")
    return
  }
  const param: params4updateVideoLecture = {
    beginning: curLecture.value.beginning,
    id: curLecture.value.id,
    // 改内容
    type: 2,
    updateManuscript: curLecture.value.content,
  }
  updateVideoLecture(param).then((res) => {
    if (res.success) {
      ElMessage.success("编辑成功")
      isEditing.value = false
    } else {
      ElMessage.error("保存失败")
    }
  })
}
const handleChangeHead = () => {
  const param: params4updateVideoLecture = {
    beginning: curLecture.value.beginning,
    id: curLecture.value.id,
    type: 0,
    updateManuscript: "",
  }
  updateVideoLecture(param).then((res) => {
    if (res.success) {
      ElMessage.success("修改段首成功")
      isEditing.value = false
    } else {
      ElMessage.error("修改段首失败")
    }
  })
}
</script>

<template>
  <div
    class="lecture-line-wrapper"
    :class="{ head: curLecture.beginning, selectable: stepMode == 2 }"
  >
    <span class="time-wrapper">
      {{ curLecture.time }}
    </span>
    <span
      class="content-wragipper"
      @click="change2EditMode"
      v-show="!isEditing"
      :style="stepMode == 2?'width: 75%;':'width: 80%'"
    >
      <template v-if="stepMode == 2">
        <span class="ck-content" v-html="processAllLatexEquations(curLecture.content)"></span>
      </template>
      <template v-if="stepMode == 3">
        <span class="ck-content" v-html="processAllLatexEquations(curLecture.content)"></span>
      </template>
    </span>
    <span v-show="isEditing" class="content-wrapper">
      <el-input
        ref="inputRef"
        v-model="curLecture.content"
        @change="handleSaveContent"
        @keydown.enter="change2ReadMode"
        @blur="change2ReadMode"
      ></el-input>
    </span>
    <span v-if="stepMode == 2" class="select-wrapper">
      <!-- element-plus^2.6新增了true-value和false-value字段，但是目前用的是2.4版本，暂不支持 => 所以让后端改成true/false了^^ -->
      <!--      <el-checkbox v-model="curLecture.beginning" @change="handleSaveContent" label="段首" :true-value="1" :false-value="0" :checked="curLecture.beginning == 1" :disabled="lectureIndex == 0"/>-->
      <el-checkbox
        v-model="curLecture.beginning"
        @change="handleChangeHead"
        label="段首"
        :checked="curLecture.beginning"
        :disabled="lectureIndex == 0"
      />
    </span>
  </div>
</template>

<style scoped>
.lecture-line-wrapper {
  word-break: break-all;
  overflow: hidden;
  flex-wrap: wrap;
  font-weight: 400;
  display: flex;
  width: 100%;
  flex-direction: row;
  min-height: 46px;
  align-items: center;
  padding: 13px 10px;
  justify-content: space-between;
  &.selectable {
    .time-wrapper {
      display: flex;
      /* width: 160px; */
      line-height: 22px;
      justify-content: center;
      flex-direction: row;
      margin-right: 20px;
      align-items: center;
    }
    .content-wrapper {
      width: 75%;
      line-height: 22px;
      word-break: break-all;
      /*overflow: hidden;*/
      /*text-overflow: ellipsis;*/
      /*white-space: nowrap;*/
    }
    .select-wrapper {
      display: flex;
      width: 60px;
      justify-content: center;
      align-items: center;
      margin-left: 40px;
      .el-checkbox {
        height: 22px;
      }
    }
  }
  &:not(.selectable) {
    .time-wrapper {
      display: flex;
      /* width: 72px; */
      line-height: 22px;
      justify-content: flex-start;
      /*overflow: hidden;*/
      /*text-overflow: ellipsis;*/
      /*white-space: nowrap;*/
    }
    .content-wrapper {
      display: flex;
      align-items: center;
      width: 100%;
      height: 100%;
    }
  }
}
.head {
  background-color: var(--color-light);
}
.content-wragipper {
  width: 75%;
}
</style>
