import { userInfoStore } from "@/stores/userInfo"
import {
  createRouter,
  createWebHistory,
  type NavigationGuardNext,
  type RouteLocationNormalized,
} from "vue-router"

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    // {
    //   path: '/',
    //   name: 'noLogin',
    //   component: () => import('../views/nologin.vue'),
    // },
    {
      path: "/home/",
      name: "home",
      component: () => import("../views/index.vue"),
      redirect: "/home/<USER>",
      children: [
        // {
        //   path: '',
        //   name: 'default',
        //   component: () => import('../views/myProject/index.vue'),
        // },
        {
          path: "myProject",
          name: "myProject",
          component: () => import("../views/myProject/index.vue"),
        },
        {
          path: "recycleStation",
          name: "recycleStation",
          component: () => import("../views/recycleStation/index.vue"),
        },
        {
          path: "projectExamine",
          name: "projectExamine",
          component: () => import("../views/projectExamine/index.vue"),
        },
        {
          path: "projectDisplay",
          name: "projectDisplay",
          component: () => import("../views/projectDisplay/index.vue"),
        },
        {
          path: "projectSearch",
          name: "projectSearch",
          component: () => import("../views/projectSearch/index.vue"),
        },
        {
          path: "userManage",
          name: "userManage",
          component: () => import("../views/userManage/index.vue"),
        },
        {
          path: "characterManage",
          name: "characterManage",
          component: () => import("../views/characterManage/index.vue"),
        },
        {
          path: "examineManage",
          name: "examineManage",
          component: () => import("../views/examineManage/index.vue"),
        },
        {
          path: "tagManage",
          name: "tagManage",
          component: () => import("../views/tagManage/index.vue"),
        },
        {
          path: "projectPreview",
          name: "projectPreview",
          component: () => import("../views/projectPreview/index.vue"),
        },
        {
          path: "create",
          name: "create",
          component: () => import("../views/createProject/index.vue"),
          children: [
            {
              path: "",
              name: "step0",
              component: () =>
                import("../views/createProject/step0/selectForm.vue"),
            },
            {
              path: "step1",
              name: "step1",
              component: () => import("../views/createProject/step1/step1.vue"),
            },
            {
              path: "step2v2",
              name: "step2v2",
              component: () => import("../views/createProject/step2/step2.vue"),
            },
            {
              path: "step2",
              name: "step2",
              component: () => import("../views/createProject/step2v2/index.vue"),
            },
            {
              path: "step3",
              name: "step3",
              component: () => import("../views/createProject/step3/step3.vue"),
            },
          ],
        },
        {
          path: "examine",
          name: "examine",
          component: () =>
            import("../views/projectExamine/components/projectExamine.vue"),
        },
        {
          path: "config",
          name: "config",
          component: () =>
            import("../views/characterManage/components/configPage.vue"),
        },
      ],
    },
    {
      path: "/:catchAll(.*)",
      redirect: "/home",
    },
  ],
})

router.beforeEach((_to, _from, next) => {
  const userinfo = userInfoStore()
  const userId = userinfo.getUserId()
  if (userId) {
    next()
  } else {
    userinfo
      .getUserInfo()
      .then(() => {
        next()
      })
      .catch((error) => console.error(error))
  }
})
export default router
