import { http } from '@/apis';
import type {APIResponse} from "@/utils/type";

export function getTmpSecretKey(prjId: number): Promise<APIResponse> {
    return http.request({
        method: 'get',
        url: `/cos-sts-client/getTemporaryKey/${prjId}`,
    });
}
export function uploadVideo2Backend(secId: number, videoKey: string): Promise<APIResponse> {
    return http.request({
        method: 'post',
        url: '/video/updateVideo',
        data: {
            sectionId: secId,
            url: videoKey,
        }
    });
}
export function getM3U8Key(mp4Key: string): Promise<APIResponse> {
    return http.request({
        method: 'post',
        url: '/video/getProcessedKey',
        data: {
            mp4Key: mp4Key,
        }
    });
}
// // 获取预览视频
// export function getM3U8Key(): Promise<APIResponse> {
//     return http.request({
//         method: 'get',
//         url: '/cos-sts-client',
//     });
// }