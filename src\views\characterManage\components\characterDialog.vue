<script setup lang="ts">
import {ref, onMounted} from "vue";
import {type params2character} from "@/apis/path/characterManage";
import {ElMessage} from "element-plus";
import MyButton from "@/views/common/myButton.vue";

const emit = defineEmits(['submitAdd', 'submitEdit']);
const isConfigDialogShow = ref(false);
const dialogTitle = ref('');
const characterName = ref('');
const curMode = ref(0);
const editId = ref();
const showDialog = (id?: number, name?: string) => {
  if (id) {
    curMode.value = 2;
    editId.value = id;
    characterName.value = name as string;
  }
  else {
    curMode.value = 1;
  }
  // curMode: 1新建 | 2编辑
  switch (curMode.value) {
    case 1: dialogTitle.value = '新建角色';
      break;
    case 2: dialogTitle.value = '编辑角色';
      break;
    default: ElMessage.error('初始化弹窗组件出错');
      break;
  }
  isConfigDialogShow.value = true;
}
const handleSubmit = () => {
  if (!characterName.value) {
    ElMessage.warning('请输入角色名称');
    return;
  }
  const param = ref<params2character>({
    name: characterName.value,
    oid: editId.value,
  });
  switch (curMode.value) {
    case 1: emit('submitAdd', param.value);
    break;
    case 2: emit('submitEdit', param.value);
    break;
    default: ElMessage.error('提交出错');
    break;
  }
  handleClose();
}
const handleClose = () => {
  isConfigDialogShow.value = false;
}
onMounted(() => {
});
defineExpose({
  showDialog
});
</script>

<template>
  <el-dialog
      :close-on-click-modal="false"
      v-model="isConfigDialogShow"
      width="596"
  >
    <template #header="{titleId}">
      <div :id="titleId" class="title">
        {{dialogTitle}}
      </div>
    </template>
    <div class="content-container">
      <div class="content-line">
        <span style="white-space: nowrap;">角色名称</span>
        <el-input class="input" v-model="characterName" placeholder="请输入角色名称"></el-input>
      </div>
    </div>
    <template #footer>
      <div class="footer">
        <div class="btn-group">
          <my-button type="light" @click="handleClose">关闭</my-button>
          <my-button @click="handleSubmit">确认</my-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
/* el-dialog的渲染是通过teleport直接挂到body下边，在这里写样式不生效，所以把样式写到全局(App.vue)了 */
.title {
  font-size: 16px;
  font-family: var(--font-family-text);
}
.content-container {
  font-family: var(--font-family-text);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .content-line {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 35px;
    margin-bottom: 20px;
    .input{
      --el-color-primary: var(--el-border-color);
      margin-left: 35px;
      width: 420px;
    }
  }
}
.footer {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
  .btn-group {
    display: flex;
    justify-content: space-between;
    width: 280px;
  }
}
.el-select-dropdown__item {
  color: var(--color-black);
  font-family: var(--font-family-text);
  &:hover * {
    font-weight: 600;
  }
  &.selected {
    color: var(--color-primary);
  }
}
</style>