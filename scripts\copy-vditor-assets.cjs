const fs = require('fs');
const path = require('path');

// 递归复制目录函数
function copyDir(src, dest) {
  // 确保目标目录存在
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true });
  }

  // 读取源目录内容
  const entries = fs.readdirSync(src, { withFileTypes: true });

  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);

    if (entry.isDirectory()) {
      // 递归复制子目录
      copyDir(srcPath, destPath);
    } else {
      // 复制文件
      fs.copyFileSync(srcPath, destPath);
    }
  }
}

// 主函数
function main() {
  const sourceDir = path.join(__dirname, '..', 'node_modules', 'vditor', 'dist');
  const targetDir = path.join(__dirname, '..', 'dist', 'dist');

  try {
    if (fs.existsSync(sourceDir)) {
      console.log('开始复制vditor资源...');
      copyDir(sourceDir, targetDir);
    } else {
      console.error('未找到vditor资源目录:', sourceDir);
      process.exit(1);
    }
  } catch (error) {
    console.error('复制vditor资源失败:', error.message);
    process.exit(1);
  }
}

// 执行主函数
main();
