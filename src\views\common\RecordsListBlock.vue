<script setup lang="ts">
import { ref, watch } from "vue";
import { renderMarkdown } from "@/utils/markdown";

const recordsList = ref<any[]>([]);
const curShowIndex = ref(-1);
const props = defineProps({
  list: {
    type: Array,
  },
});

// 处理折叠
const handleExpend = (index: number) => {
  if (curShowIndex.value === index) {
    curShowIndex.value = -1;
  } else {
    curShowIndex.value = index;
  }
};
</script>
<template>
  <div class="wrapper">
    <div v-if="props.list.length === 0">暂无记录</div>
    <span
      v-else
      class="records"
      v-for="(item, index) in props.list"
      :key="index"
    >
      <span class="record-item" @click="handleExpend(index)">
        <el-row style="display: flex; width: 100%">
          <el-col :span="8"
            ><span>{{ item.createTime }}</span></el-col
          >
          <el-col :span="6"
            ><span>{{
              item.processorName ? item.processorName : "-"
            }}</span></el-col
          >
          <el-col :span="6"
            ><span>{{ item.actionName }}</span></el-col
          >
          <el-col :span="3"
            ><span>{{
              item.auditResult ? item.auditResult : "-"
            }}</span></el-col
          >
          <el-col :span="1"
            ><span
              style="
                display: flex;
                height: 100%;
                align-items: center;
                justify-content: center;
              "
            >
              <img
                src="@/assets/images/common/u1705.svg"
                :class="{ 'rotate-90deg': curShowIndex === index }" /></span
          ></el-col>
        </el-row>
      </span>
      <span class="record-detail" v-if="curShowIndex === index">
        <el-form>
          <el-form-item label="审核时间：">{{ item.createTime }}</el-form-item>
          <el-form-item label="审核环节：">{{ item.actionName }}</el-form-item>
          <el-form-item label="审核人：">{{
            item.processorName ? item.processorName : "-"
          }}</el-form-item>
          <el-form-item label="审核结果：">{{
            item.auditResult ? item.auditResult : "-"
          }}</el-form-item>
          <el-form-item label="审核意见："></el-form-item>
          <el-form-item>
            <span
              v-if="item.auditOpinion"
              class="opinion ck-content"
              v-html="item.auditOpinion"
            ></span>
            <span v-else class="opinion"> 暂无 </span>
          </el-form-item>
        </el-form>
      </span>
    </span>
  </div>
</template>
<style scoped>
.wrapper {
  display: flex;
  flex-direction: column;
  background-color: var(--color-light);
  padding: 10px;
  font-family: var(--font-family-text);
  .records {
    display: flex;
    width: 100%;
    flex-direction: column;
    margin-bottom: 10px;
    .record-item {
      display: flex;
      width: 100%;
      background-color: white;
      border: 1px solid var(--color-boxborder);
      border-radius: 3px;
      justify-content: space-between;
      cursor: pointer;
      padding: 5px;
      .record-detail {
        display: flex;
        width: 100%;
      }
    }
  }
}
</style>
<style>
@keyframes rotate90deg {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(90deg);
  }
}
.rotate-90deg {
  animation: rotate90deg 0.07s linear forwards; /* 0.07秒内完成动画，线性速度，完成后保持状态 */
}
.el-form-item {
  margin: 0;
  .opinion {
    padding: 10px;
    display: flex;
    width: 100%;
    background-color: white;
    border-radius: 3px;
    border: 1px solid var(--color-boxborder);
  }
}
</style>
