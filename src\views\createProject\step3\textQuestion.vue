<script setup lang="ts">
import ModeTypeSwitcher from "@/views/createProject/step3/modeTypeSwitcher.vue";
import QuestionList from "@/views/createProject/step3/questionList.vue";
import { inject, ref, watch } from "vue";
import useLineWord from "@/utils/lineWord";
import QuestionDialog from "@/views/createProject/step3/questionDialog.vue";
import { ElMessage } from "element-plus";

import type { questionType, tagType } from "@/utils/type";
import { qMode, qModeDict, qType, qTypeDict } from "@/utils/constant";
import { findKeyByValue } from "@/utils/func";
import {
  saveQuestion,
  type params4addQuestion,
} from "@/apis/path/lineWordApis";
import { deleteQuestion4Text, getTextSection } from "@/apis/path/createProject";
import ContentRenderer from "@/components/ContentRenderer.vue";
import { highlightCodeBlocks } from "@/utils/highlightCodeBlocks";

// const { getQuestionList } = useLineWord();
const props = defineProps({
  curProjectId: {
    type: Number,
    required: true,
  },
  curSectionId: {
    type: Number,
    required: true,
  },
});
const curPrjId = ref();
const curSecId = ref();
const curCntId = ref();
const questionDialogRef = ref();
const textContent = ref();
const quesList = ref<questionType[]>([]);
const isDialogShowing = ref(false); // 通过问题列表点开的弹窗是本组件的孙子组件，不好用ref获得显示状态，所以本组件单独维护一个
// const curMode = ref<number>(0); // 0：reading || 1：questioning
const isOperable = ref();
isOperable.value = !inject("displaying") as boolean;

const getSectionData = () => {
  if (curSecId.value) {
    getTextSection(curSecId.value).then((res) => {
      if (res.success) {
        const textSection = res.data.wordsContent.projectSections[0];
        textContent.value = textSection.prText;
        curCntId.value = textSection.contentId;
        // const qList = res.data.wordsContent.questionAndAnswers;
        // quesList.value = qList?.map((q) => {
        //   return {
        //     qId: q.question.oid,
        //     isValid: q.question.valid,
        //     relatedText: q.question.associatedWords,
        //     qContent: q.question.keyWords,
        //     qType: qTypeDict[q.question.questionType], // 是什么 | 为什么
        //     qMode: q.question.questionNecessity, // 必要 | 参考
        //     klg: q.answers[0].answerKlgs?.map(
        //       (i: { klgCode: string; title: string }) => {
        //         return {
        //           id: i.klgCode,
        //           name: i.title,
        //         };
        //       }
        //     ),
        //     explanation: q.answers[0].answerExplanation,
        //   };
        // });
      }
    });
  }
};
watch(
  () => props,
  (newValue, oldValue) => {
    curSecId.value = newValue.curSectionId;
    curPrjId.value = newValue.curProjectId;
    getSectionData();
  },
  { deep: true, immediate: true }
);

// const refreshTextContent = (newTextContent: string) => {
//   if (!isOperable.value) {
//     console.log("不应该到这里的");
//     return;
//   }
//   textContent.value = newTextContent;
//   if (curMode.value == 0) {
//     markWord();
//   }
// };
// const handleAddQuestion = (richText: string, text: string) => {
//   if (!isOperable.value) {
//     console.log("不应该到这里的");
//     return;
//   }
//   let ques: questionType = {
//     qId: -1,
//     isValid: true,
//     relatedText: richText,
//     qContent: text,
//     qType: qType.what, // 是什么 | 为什么
//     qMode: qMode.ness, // 必要 | 参考
//     klg: [],
//     explanation: "",
//   };
//   questionDialogRef.value.showDialog([ques], 0); // mode=0：add question
// };
// const handleWord = (el: any) => {
//   // 提问模式禁止点击
//   if (curMode.value == 1) {
//     return;
//   }
//   // 打开窗口时禁止点击（窗口的关联文本字段是html渲染的，所以会有onclick点击事件）
//   if (isDialogShowing.value) {
//     return;
//   }
//   const id = el.getAttribute("data-qid");
//   if (id == "") {
//     // ElMessage({
//     //   type: 'error',
//     //   message: '没有qid'
//     // });
//     return;
//   }
//   getQuestionList(id).then((questionList) => {
//     questionDialogRef.value.showDialog(questionList, 2);
//   });
// };
const handleOpenDialog = () => (isDialogShowing.value = true);
const handleCloseDialog = () => (isDialogShowing.value = false);
const handleSubmitQuestion = (newQuestion: questionType) => {
  if (!isOperable.value) {
    console.log("不应该到这里的");
    return;
  }
  handleCloseDialog();
  const param: params4addQuestion = {
    prjId: curPrjId.value,
    secId: curSecId.value,
    contentId: curCntId.value,
    relatedText: newQuestion.relatedText, // 给后端传的是划词产生的富文本源码
    content: newQuestion.qContent, // 可以编辑的文本内容
    type: findKeyByValue(newQuestion.qType, qTypeDict) as string, // '是什么' | '为什么'
    mode: newQuestion.qMode,
    klgCode: newQuestion.klg?.map((klg: tagType) => klg.id), // 关联知识点
    explanation: newQuestion.explanation, // 说明
  };
  saveQuestion(param)
    .then((res) => {
      if (res.success) {
        quesList.value?.push({
          isValid: true,
          qId: res.data.questionId,
          relatedText: newQuestion.qContent,
          qContent: newQuestion.qContent,
          qType: newQuestion.qType,
          qMode: newQuestion.qMode,
          klg: newQuestion.klg,
          explanation: newQuestion.explanation,
        });
        // refreshTextContent(res.data.prText);
        ElMessage.success("保存问题成功");
      } else {
        ElMessage.error(res.message);
      }
    })
    .catch();
};
// const handleDeleteQuestion = (deleteQuestionId: number) => {
//   if (!isOperable.value) {
//     console.log("不应该到这里的");
//     return;
//   }
//   deleteQuestion4Text(deleteQuestionId)
//     .then((res) => {
//       if (res.success) {
//         ElMessage.success("删除问题成功");
//         quesList.value = quesList.value.filter(
//           (q: questionType) => q.qId != deleteQuestionId
//         );
//         // allQuestionList.value = allQuestionList.value.filter((q: questionType) => q.qId != deleteQues.qId);
//         // quesList.value = quesList.value.filter((q: questionType) => q.qId != deleteQues.qId);
//         // emits('delete', res.data.result);
//         refreshTextContent(res.data.result);
//       } else {
//         ElMessage.error("删除问题失败");
//       }
//     })
//     .catch();
// };
// const handleEditQuestion = () => {
//   if (!isOperable.value) {
//     console.log("不应该到这里的");
//     return;
//   }
//   // 通信逻辑放到子组件questionList里，本方法只用于示意孙子组件questionDialog已经关闭，可以恢复点击高亮词汇的功能
//   handleCloseDialog();
// };
// onMounted(() => {
//   window.handleWord = handleWord;
//   const content = document.getElementById("htmlContent");

//   content!.addEventListener("mouseup", () => {
//     if (curMode.value == 0) {
//       return;
//     }
//     const result = handleLineWord();
//     if (result != "") {
//       handleAddQuestion(result, window.getSelection()!.toString());
//     }
//   });
// });
</script>

<template>
  <div class="text-question-wrapper">
    <div class="left-wrapper">
      <!-- <div v-if="isOperable" style="margin-bottom: 10px">
        <mode-type-switcher :mode="curMode" @changeMode="handleChangeMode">
          <template v-slot:mode0> 阅读模式 </template>
          <template v-slot:mode1> 提问模式 </template>
        </mode-type-switcher>
      </div> -->
      <!-- <div>{{ 11111 }}</div> -->
      <ContentRenderer
        :content="highlightCodeBlocks(textContent)"
      ></ContentRenderer>
    </div>
    <!-- <div class="right-wrapper">
      <question-list @open="handleOpenDialog" @close="handleCloseDialog" @edit="handleEditQuestion"
        @delete="handleDeleteQuestion" :question-list="quesList"></question-list>
    </div> -->
  </div>
  <question-dialog
    @open="handleOpenDialog"
    @close="handleCloseDialog"
    @submit="handleSubmitQuestion"
    ref="questionDialogRef"
  ></question-dialog>
</template>

<style scoped>
:deep(.highLight) {
  color: var(--color-primary);
  cursor: pointer;
}

:deep(.highLight:hover) {
  font-weight: 700;
}

.text-question-wrapper {
  display: flex;
  flex-direction: row;
  margin-bottom: 55px;
  width: 100%;

  .left-wrapper {
    flex-direction: column;
    width: 100%;
    word-break: break-all;
    margin-right: 20px;
    display: flex;

    .content-container {
      border: 1px solid var(--color-boxborder);
      padding: 10px 20px;
    }
  }

  .right-wrapper {
    width: 465px;
    display: flex;
  }
}
</style>
