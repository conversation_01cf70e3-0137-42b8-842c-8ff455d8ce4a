<script setup lang="ts">
import Header from "@/views/common/header.vue";
import leftMenu from "@/views/common/leftMenu.vue";
import { RouterView } from "vue-router";
</script>

<template>
  <div class="app-container">
    <div class="header">
      <Header></Header>
    </div>
    <div class="content-container">
      <div class="left">
        <left-menu></left-menu>
      </div>
      <span class="main">
        <div class="mainWrapper">
          <router-view></router-view>
        </div>
      </span>
    </div>
  </div>
</template>

<style scoped>
.app-container {
  font-family: var(--text-family);
  background-color: rgba(242, 242, 242, 1);
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  word-break: break-all;
  :deep(pre) {
    width: max-content;
    min-width: 100%;
  }

  .header {
    display: flex;
    flex-direction: row;
    height: 60px;
    background-color: rgba(255, 255, 255, 0.996);
    box-shadow: rgba(149, 157, 165, 0.2) 0px 5px 24px;
    margin-bottom: 10px;
  }

  .content-container {
    height: calc(100vh - 70px);
    display: flex;
    flex-direction: row;
    overflow: hidden;

    .left {
      background-color: white;
      height: 100%;
      width: 200px;
    }
    .main {
      width: 100%;
      display: flex;
      justify-content: center;
      overflow-y: auto;
      overflow-x: hidden;
      position: relative;

      &::-webkit-scrollbar {
        width: 5px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: #888;
        border-radius: 6px;
        height: 50px;
      }

      &::-webkit-scrollbar-track {
        background-color: #f1f1f1;
      }
    }
  }
}
</style>
