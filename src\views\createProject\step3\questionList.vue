<script setup lang="ts">

import ModeTypeSwitcher from "@/views/createProject/step3/modeTypeSwitcher.vue";
import { inject, ref, watch } from "vue";
import type { questionType, tagType } from "@/utils/type";
import QuestionDialog from "@/views/createProject/step3/questionDialog.vue";
import { type params4editQuestion, editQuestion } from "@/apis/path/lineWordApis";
import { findKeyByValue } from "@/utils/func";
import { qMode, qTypeDict, qType } from "@/utils/constant";
import { deleteQuestion4Text } from "@/apis/path/createProject";
import { ElMessage, ElMessageBox } from "element-plus";
import { all } from "axios";

const props = defineProps({
  questionList: {
    type: Array as () => questionType[],
    default: () => [],
  },
  // isQuestionOperable: {
  //   type: Boolean,
  //   default: true,
  // }
});
const emits = defineEmits(["edit", "open", "close", "delete"]);
const allQuestionList = ref<questionType[]>([]);
const quesList = ref<questionType[]>([]);
const curMode = ref<number>(qMode.ness);
const questionDialogRef = ref();
const isOperable = ref();
isOperable.value = !inject('displaying') as boolean;
const filterQuestionList = () => {
  quesList.value = allQuestionList.value.filter((q: questionType) => q.qMode == curMode.value);
  // console.log('quesList.value', quesList)
}
watch(() => props.questionList, (newVal, oldVal) => {
  // console.log('questionList: watched ' + JSON.stringify(newVal.map(q=>q.qContent),null,2));
  allQuestionList.value = [...newVal];
  // 下边这些写法不行！
  // Object.assign(allQuestionList.value, newVal);
  // allQuestionList.value = newVal;
  filterQuestionList();
}, { deep: true, immediate: true });
const handleChangeMode = (newMode: number) => {
  curMode.value = newMode; // 0 | 1
  filterQuestionList();
}
const handleEditQuestion = (editQustion: questionType) => {
  if (!editQustion.isValid) {
    return;
  }
  if (isOperable.value) {
    questionDialogRef.value.showDialog([editQustion], 1);
  }
  else {
    questionDialogRef.value.showDialog([editQustion], 2);
  }
}
const handleDelete = (deleteQues: questionType) => {
  if (!isOperable.value) {
    console.log('不应该到这里的');
    return;
  }
  if (!deleteQues.isValid) {
    emits('delete', deleteQues.qId);
    return;
  }
  ElMessageBox.confirm(
    '确定删除问题吗？',
    '删除问题',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    emits('delete', deleteQues.qId);
  }).catch()
}
const handleSubmitEditQuestion = (newQuestion: questionType) => {
  if (!isOperable.value) {
    console.log('不应该到这里的');
    return;
  }
  const param: params4editQuestion = {
    qid: newQuestion.qId,
    associatedWords: newQuestion.relatedText, // 给后端传的是划词产生的富文本源码
    keyWords: newQuestion.qContent, // 可以编辑的文本内容
    questionType: findKeyByValue(newQuestion.qType, qTypeDict) as string, // '是什么' | '为什么'
    questionNecessity: newQuestion.qMode,
    answerKlgs: newQuestion.klg?.map((klg: tagType) => klg.id).join('@@'), // 关联知识点
    answerExplanation: newQuestion.explanation, // 说明
  }
  editQuestion(param).then(res => {
    if (res.success) {
      const index = allQuestionList.value.findIndex((q: questionType) => q.qId === newQuestion.qId);
      if (index != -1) {
        allQuestionList.value[index] = { ...newQuestion };
        filterQuestionList();
        ElMessage.success('编辑成功');
        emits('edit');
      }
      else ElMessage.error('编辑失败');
    }
    else ElMessage.error(res.message);
  }).catch();
}
</script>

<template>
  <div class="question-list-wrapper">
    <div class="head-wrapper">
      <mode-type-switcher :mode-values="[1, 2]" :mode="curMode" @changeMode="handleChangeMode">
        <template v-slot:mode0>
          必要问题
        </template>
        <template v-slot:mode1>
          参考问题
        </template>
      </mode-type-switcher>
      <!-- TODO：差个图标 -->
      <span v-if="isOperable" class="auto-match">自动匹配</span>
    </div>
    <div class="content-wrapper">
      <div @click="handleEditQuestion(item)" v-for="item in quesList" :key="item.qId" class="question"
        :class="[{ 'invalid': !item.isValid }, { 'lackAnswer': (item.klg.length == 0) }]">
        <span class="ck-content" v-html="item.qContent"></span>
        <div style="display: flex;flex-direction: row;">
          <div v-if="item.qType === qType.how">怎么做</div>
          <div v-else-if="item.qType === qType.what">是什么</div>
          <div v-else>为什么</div>

          <span v-if="isOperable" class="icon" @click.stop="handleDelete(item)">×</span>
        </div>
      </div>
    </div>
  </div>
  <question-dialog @open="emits('open')" @close="emits('close')" @submit="handleSubmitEditQuestion"
    ref="questionDialogRef"></question-dialog>
</template>

<style scoped>
.question-list-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;

  .head-wrapper {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-end;
    margin-bottom: 10px;

    .auto-match {
      font-size: 14px;
      color: var(--color-primary);
      cursor: pointer;

      &:hover {
        font-weight: 600;
      }
    }
  }

  .question {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    margin-bottom: 10px;
    width: 100%;
    height: 35px;
    color: white;
    border: 1px solid var(--color-primary);
    background-color: var(--color-primary);

    &:hover {
      font-weight: 600;
      cursor: pointer;
    }

    .icon {
      padding: 0 5px;
      cursor: pointer;
    }

    &.invalid {
      color: var(--color-invalid);
      border: 1px solid var(--color-boxborder);
      background-color: var(--color-light);

      &:hover {
        font-weight: 400;
        cursor: not-allowed;
      }
    }

    &.lackAnswer {
      color: black;
      background-color: var(--color-light);
      border: 1px solid var(--color-boxborder);
    }
  }
}
</style>