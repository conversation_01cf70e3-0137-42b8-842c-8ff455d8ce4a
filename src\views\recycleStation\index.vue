<script setup lang="ts">
import FormSwitch from "@/views/common/formSwitch.vue";
import { nextTick, onMounted, ref, watchEffect } from "vue";
import {
  getRecycleList,
  type key2recycleList,
  recoverRecycleList,
  deleteRecycleList,
} from "@/apis/path/recycleStation";
import type { recyclePrjInfo } from "@/utils/type";
import { decodeData } from "@/utils/func";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
import {
  prjForm,
  prjState,
  prjType,
  stateDict,
  typeDict,
} from "@/utils/constant";
import MyButton from "@/views/common/myButton.vue";
import MyFlipper from "@/views/common/myFlipper.vue";
import { renderMarkdown } from "@/utils/markdown";

const router = useRouter();
const prjTable = ref();
const curForm = ref("");
const curState = ref(prjState.default.toString());
const curType = ref(prjType.default.toString());
const tableData = ref();
const searchKey = ref("");
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const multipleSelection = ref<recyclePrjInfo[]>([]);
const handleSelect = (form: prjForm) => {
  curForm.value = form.toString() == "3" ? "" : form.toString();
  getRckList();
};
const handleSearch = (key: string) => {
  searchKey.value = key;
  getRckList();
};

const getRckList = () => {
  const param = ref<key2recycleList>({
    current: currentPage.value,
    limit: pageSize.value,
    prForm: curForm.value,
    prType: curType.value,
    prName: searchKey.value,
  });
  getRecycleList(param.value)
    .then((res) => {
      if (res.success) {
        tableData.value = res.data.list?.map((prj: recyclePrjInfo) =>
          decodeData(prj)
        );
        total.value = res.data.total;
      } else {
        ElMessage.error(res.message);
      }
    })
    .catch();
};
const handleDelete = (id?: number) => {
  let idList = [];
  if (id) {
    idList.push(id);
  } else {
    idList = multipleSelection.value?.map((prj: recyclePrjInfo) => prj.id);
  }
  if (!id && multipleSelection.value.length == 0) {
    ElMessage.error("请选择项目");
  } else {
    ElMessageBox.confirm("是否确定销毁？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        deleteRecycleList(idList)
          .then((res) => {
            if (res.success) {
              ElMessage.success("销毁成功");
              getRckList();
            } else {
              ElMessage.error("销毁出现异常: " + res.message);
            }
          })
          .catch();
        getRckList();
      })
      .catch();
  }
};
const handleRecover = (id?: number) => {
  let idList = [];
  if (id) {
    idList.push(id);
  } else {
    idList = multipleSelection.value?.map((prj: recyclePrjInfo) => prj.id);
  }
  if (!id && multipleSelection.value.length == 0) {
    ElMessage.error("请选择项目");
  } else {
    ElMessageBox.confirm("是否确定恢复？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        recoverRecycleList(idList)
          .then((res) => {
            if (res.success) {
              ElMessage.success("恢复成功");
              getRckList();
            } else {
              ElMessage.error("恢复出现异常:" + res.message);
            }
          })
          .catch();
        getRckList();
      })
      .catch();
  }
};
const handleChangePage = (newPage: number) => {
  currentPage.value = newPage;
  getRckList();
};
const handleSelectionChange = (val: recyclePrjInfo[]) => {
  multipleSelection.value = val;
};
const indexMethod = (index: number) => {
  return (currentPage.value - 1) * pageSize.value + index + 1;
};
const setCur = ($event: any) => {
  curType.value = $event.curType;
  getRckList();
};

onMounted(() => {
  getRckList();
  document.addEventListener("visibilitychange", () => {
    if (!document["hidden"]) {
      getRckList();
      // console.log('出现');
    } else {
      //隐藏
      // console.log('隐藏');
    }
  });
});
</script>

<template>
  <div class="main-container">
    <form-switch
      @search="handleSearch"
      @select="handleSelect"
      @cur="setCur($event)"
      placeholder="请输入项目标题"
    ></form-switch>
    <div class="content-container">
      <div class="toolbar">
        <span class="button-group">
          <my-button type="primary" @click="handleRecover()"
            >批量恢复</my-button
          >
          <my-button type="primary" @click="handleDelete()">批量销毁</my-button>
        </span>
      </div>
      <div class="line"></div>
      <el-table
        ref="prjTable"
        :data="tableData"
        @selection-change="handleSelectionChange"
        style="width: 100%"
        empty-text="暂无数据"
      >
        <el-table-column type="selection" width="50"></el-table-column>
        <el-table-column
          type="index"
          :index="indexMethod"
          label="序号"
          width="80"
        >
        </el-table-column>
        <el-table-column
          class="notFixWidth"
          prop="prname"
          label="项目名称"
          width="334"
        >
          <template #default="scope">
            <span style="width: 100%; justify-content: flex-start">
              <span
                class="prname ck-content"
                v-html="renderMarkdown(scope.row.prname)"
              ></span>
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="prform"
          label="项目形式"
          width="124"
        ></el-table-column>
        <el-table-column
          prop="prtype"
          label="项目类型"
          width="192"
        ></el-table-column>
        <el-table-column
          prop="deltime"
          label="销毁时间"
          width="183"
        ></el-table-column>
        <el-table-column label="操作" width="177">
          <template #default="scope">
            <span class="operationBlock">
              <span @click="handleRecover(scope.row.id)" class="operationBtn"
                >恢复</span
              >
              <span @click="handleDelete(scope.row.id)" class="operationBtn"
                >销毁</span
              >
            </span>
          </template>
        </el-table-column>
      </el-table>
      <my-flipper
        @change-page="handleChangePage"
        :current="currentPage"
        :page-size="pageSize"
        :total="total"
      ></my-flipper>
    </div>
  </div>
</template>

<style scoped>
.main-container {
  font-family: var(--font-family-text);
  color: var(--color-black);
  width: var(--width-content);
  /* margin: 20px auto auto auto; */

  .content-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 5px 30px 30px 30px;
    background-color: white;

    .toolbar {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-direction: row;
      padding-bottom: 5px;

      .button-group {
        display: flex;
        justify-content: space-between;
        width: 270px;
      }

      /* &::after {
        content: '';
        height: 1px;
        width: 100%;
        background-color: var(--color-line);
        position: absolute;
        bottom: 0;
      } */
    }

    .select {
      width: 180px;

      &:deep(.el-input) {
        --el-input-height: 35px;
        line-height: 35px;
      }
    }

    .el-table {
      &:deep(.el-table__cell) {
        padding: 0;
      }

      &:deep(.cell) {
        justify-content: center;
        display: flex;
        align-items: center;
        min-height: 46px;
      }
    }

    .operationBlock {
      display: flex;
      width: 76px;
      justify-content: space-between;
      align-items: center;
      flex-direction: row;
      color: var(--color-primary);
      .operationBtn {
        &:hover {
          cursor: pointer;
          color: var(--color-primary);
          font-weight: 600;
          /* text-decoration: underline; */
        }
      }
    }
  }
}
</style>
