<script setup lang="ts">
import ModeTypeSwitcher from "@/views/createProject/step3/modeTypeSwitcher.vue";
import projectPreview from "@/views/projectPreview/index.vue"
import {onMounted, ref} from 'vue';
import PrjPreview from "@/views/projectPreview/components/prjPreview.vue";
import {useRoute} from "vue-router";
import ExamineInfo from "@/views/projectExamine/components/examineInfo.vue";

const route = useRoute();
const curMode = ref(0); // 0|1
const curPrjId = ref();
const curPrjForm = ref();
const handleChangeMode = (newMode: number) => {
  curMode.value = newMode;
}
onMounted(() => {
  curPrjId.value = parseInt(route.query.prjId as string);
  curPrjForm.value = route.query.prjForm;
})
</script>

<template>
  <div class="examine-wrapper">
    <mode-type-switcher :mode="curMode" @change-mode="handleChangeMode">
      <template #mode0>
        审核信息
      </template>
      <template #mode1>
        项目内容
      </template>
    </mode-type-switcher>
    <div style="width: 100%" v-show="curMode==0">
      <examine-info :prj-id="curPrjId"></examine-info>
    </div>
    <div v-show="curMode==1">
      <prj-preview :prj-id="curPrjId" :prj-form="curPrjForm"></prj-preview>
    </div>
  </div>
</template>

<style scoped>
.examine-wrapper {
  font-family: var(--font-family-text);
  background-color: white;
  color: var(--color-black);
  width: var(--width-content);
  /* margin: 20px auto auto auto; */
  padding: 12px 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
</style>