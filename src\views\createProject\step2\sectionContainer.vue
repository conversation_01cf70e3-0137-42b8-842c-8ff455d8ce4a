<script setup lang="ts">
import {onMounted, reactive, ref, watch} from "vue";
import type {simpleSectionInfo, videoSectionType} from "@/utils/type";
import VideoContent from "@/views/createProject/step2/videoContent.vue";
import {getVideoSection} from "@/apis/path/createProject";
import {ElMessage, type FormRules} from "element-plus";
import {prjForm, prjType} from "@/utils/constant";
import TextContent from "@/views/createProject/step2/textContent.vue";
import TextExamContent from "@/views/createProject/step2/textExamContent.vue";

const emits = defineEmits(['saveSectionDone', 'saveSectionDraftDone']);
const props = defineProps({
  projectId: Number,
  sectionId: Number,
  sectionName: String,
  // TODO：有空把这个showSecId也改成p/i
  showSecId: Number,
  releaseDate: String,
  sectionCount: Number,
  projectForm: {
    type: String,
    required: false,
    default: '1',
  },
  projectType: {
    type: String,
    required: false,
    default: '1',
  }
});
const prjId = ref();
const curPrjForm = ref();
const curPrjType = ref();
const secId = ref();
const rlsDate = ref();
const secName = ref();
const secCount = ref();
const contentRef = ref();
const formRef = ref(null);
const form = ref({
    secName: '',
});
const rules = ref({
  secName: [
    { required: true, message: '请输入节标题', trigger: 'blur'}
  ]
})
// const videoContentRef = ref();
watch(() => props, (newValue, oldValue) => {
  // console.log("videoSection: watched " + JSON.stringify(newValue, null, 2));
  if (newValue.showSecId == newValue.sectionId) {    // 如果没对上，那么不更新secId，videoContent还收到-1，不触发数据接口
    secId.value = newValue.sectionId;
    secCount.value = newValue.sectionCount;
    secName.value = newValue.sectionName;
    form.value.secName = secName.value;
  }
  else {
    secId.value = -1;
  }
  prjId.value = newValue.projectId;
  curPrjForm.value = newValue.projectForm;
  curPrjType.value = newValue.projectType;
  rlsDate.value = newValue.releaseDate;
}, {deep: true, immediate: true})

const receiveSecName = (sectionName: string) => {
  secName.value = sectionName;
  form.value.secName = sectionName;
}
const handleChangeSectionName = (newVal: string) => {
  contentRef.value.setSectionName(newVal);
}
const emitBubblesaveSectionDraftDone = (success: boolean) => {
  emits('saveSectionDraftDone', success);

  
}
// 冒泡提交，当容器里面的表单验证完成后，验证当前表单
const emitBubblesaveSectionDone = (success: boolean) => {
  formRef.value.validate((valid) => {
        if (valid && success) {
            // 当前和success均为true才保存
            emits('saveSectionDone', true);
        } else {
            emits('saveSectionDone', false);
        }      
    });
  
}
onMounted(()=>{
  form.value.secName = secName.value;
})
</script>

<template>
  <el-form class="form" :model="form" :rules="rules" ref="formRef" @submit.native.prevent>
    <el-form-item prop="secName">
      <el-input v-model="form.secName" @change="handleChangeSectionName" class="my-input" placeholder="请输入节标题" @keydown.enter.stop></el-input>
    </el-form-item>
  </el-form>
  <div class="section-wrapper">
  
    <!-- 视频 -->
    <template v-if="curPrjForm == prjForm.video">
      <video-content 
        ref="contentRef" 
        @saveSectionDraftDone="emitBubblesaveSectionDraftDone" 
        @saveSectionDone="emitBubblesaveSectionDone"
        :projectId="prjId" 
        :sectionId="secId" 
        :sectionCount="secCount" 
        :releaseDate="rlsDate" 
        @sendSecName="receiveSecName">
      </video-content>
    </template>
    <!-- 测试 -->
    <template v-else-if="curPrjForm == prjForm.text && curPrjType == prjType.exam">
      <text-exam-content ref="contentRef"
                         @saveSectionDraftDone="emitBubblesaveSectionDraftDone"
                         @saveSectionDone="emitBubblesaveSectionDone"
                         @sendSecName="receiveSecName"
                         :projectId="prjId"
                         :sectionId="secId"
                         :sectionCount="secCount"
                         :sectionName="secName">
      </text-exam-content>
    </template>
    <!-- 文稿 -->
    <template v-else-if="curPrjForm == prjForm.text">
      <text-content ref="contentRef"
                    @saveSectionDraftDone="emitBubblesaveSectionDraftDone"
                    @saveSectionDone="emitBubblesaveSectionDone"
                    @sendSecName="receiveSecName"
                    :projectId="prjId"
                    :sectionId="secId"
                    :sectionCount="secCount"
                    :sectionName="secName">
      </text-content>
    </template>
  
</div>
</template>

<style scoped>
.section-wrapper {
  .my-input {
    margin-bottom: 20px;
  }
  
}
.form {
  :deep(.el-form-item__content) {
  height: 40px;
  }
}
</style>