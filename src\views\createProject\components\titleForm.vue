<script setup lang="ts">
import { ref, watch } from 'vue'
const props = defineProps({
  form: {
    type: String,
    required: true,
  }
});
const curForm = ref<string>();

watch(() => props, (newValue, oldValue) => {
  curForm.value = newValue.form;
},{deep:true, immediate:true})

</script>
<template>
<div class="container">
    <span class="form" v-if="curForm == 1">
        <img src="@/assets/images/create/u3454.svg">
        视频项目
    </span>
    <span class="form" v-else-if="curForm == 2">
        <img src="@/assets/images/create/u3462.svg">
        文稿项目
    </span>
</div>
</template>
<style scoped>
.container {
    width: 100%;
    height: 56px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    .form {
        padding-left: 30px;
        margin-right: 322px;
        font-size: 16px;
        font-weight: 600;
        color: var(--color-primary);  
    }
    /* &::after {
        content: '';
        height: 1px;
        width: 100%;
        background-color: var(--color-line);
        position: absolute;
        bottom: 0;
    }    */
}

</style>