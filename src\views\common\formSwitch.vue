<script setup lang="ts">
import { ref } from "vue";
import {
  prjForm,
  prjState,
  prjType,
  stateNoDeleteDict,
  typeDict,
  sortDict,
} from "@/utils/constant";
import { Search, Menu, VideoCamera, Document } from "@element-plus/icons-vue";
const curType = ref<prjType>(prjType.default);
const curState = ref<prjState>(prjState.default);
const curTime = ref("");
const curSort = ref(0);
const selectedForm = ref<prjForm>(prjForm.all);
const searchKey = ref("");
const emit = defineEmits(["select", "search", "cur"]);
const props = defineProps({
  placeholder: {
    type: String,
    default: "请输入",
  },
  formFunc: {
    type: Boolean,
    default: true,
  },
  needCurState: {
    type: Boolean,
    default: false,
  },
  needCurTime: {
    type: Boolean,
    default: false,
  },
  needCurSort: {
    type: Boolean,
    default: false,
  },
  needSearch: {
    type: Boolean,
    default: true,
  },
});
const select = (select: prjForm) => {
  if (select) {
    selectedForm.value = select;
  }
  emit("select", selectedForm.value);
};
const search = () => {
  emit("search", searchKey.value);
};
const changePrjList = () => {
  // console.log("[curState]",curState.value,"[curType]",curType.value)
  let cur = {
    curState: curState.value,
    curType: curType.value,
    curTime: curTime.value,
    curSort: curSort.value,
  };
  emit("cur", cur);
};
</script>

<template>
  <span
    class="switchContainer"
    :style="props.formFunc ? '' : 'justify-content: flex-end;'"
  >
    <span class="btnGroup" v-if="props.formFunc">
      <span
        class="option"
        style="width: 60px"
        :class="selectedForm == prjForm.all ? 'active' : ''"
        @click="select(prjForm.all)"
      >
        <!-- <img style="margin-right: 10px" src="@/assets/images/common/u319.svg" /> -->
        <el-icon><Menu /></el-icon>
        全部
      </span>
      <span
        class="option"
        :class="selectedForm == prjForm.video ? 'active' : ''"
        @click="select(prjForm.video)"
      >
        <!-- <img style="margin-right: 10px" src="@/assets/images/common/u345.svg" /> -->
        <el-icon><VideoCamera /></el-icon>
        视频项目
      </span>
      <span
        class="option"
        :class="selectedForm == prjForm.text ? 'active' : ''"
        @click="select(prjForm.text)"
      >
        <!-- <img style="margin-right: 10px" src="@/assets/images/common/u342.svg" /> -->
        <el-icon><Document /></el-icon>
        文稿项目
      </span>
    </span>
    <span class="rightContent">
      <span class="select-group">
        <el-date-picker
          v-if="$props.needCurTime"
          @change="changePrjList"
          value-format="YYYY-MM-DD HH:mm:ss"
          style="width: 200px; margin-right: 10px"
          v-model="curTime"
          type="date"
          placeholder="请选择发布日期"
          :class="curTime ? 'highlight' : ''"
          popper-class="primary"
        />
        <el-select
          @change="changePrjList"
          v-model="curState"
          placeholder="全部状态"
          class="select"
          style="margin-right: 10px"
          v-if="$props.needCurState"
        >
          <el-option
            v-for="(value, key) in stateNoDeleteDict"
            :key="key"
            :label="key"
            :value="value"
            :class="curState === value ? 'highlight' : ''"
          >
            <div>
              <span class="option">{{ key }}</span>
            </div>
          </el-option>
        </el-select>
        <el-select
          @change="changePrjList"
          v-model="curType"
          placeholder="全部类型"
          class="select"
          style="margin-right: 10px"
        >
          <el-option
            v-for="(value, key) in typeDict"
            :key="key"
            :label="key"
            :value="value"
            :class="curType === value ? 'highlight' : ''"
          >
            <div>
              <span class="option">{{ key }}</span>
            </div>
          </el-option>
        </el-select>
        <el-select
          v-if="$props.needCurSort"
          @change="changePrjList"
          v-model="curSort"
          placeholder="排序方式"
          style="margin-right: 10px"
          class="select"
        >
          <el-option
            v-for="(value, key) in sortDict"
            :key="key"
            :label="key"
            :value="value"
            :class="curSort === value ? 'highlight' : ''"
          >
            <div>
              <span class="option">{{ key }}</span>
            </div>
          </el-option>
        </el-select>
        <el-input
          v-if="$props.needSearch"
          class="input"
          v-model="searchKey"
          @keydown.enter="search"
          :placeholder="props.placeholder"
        >
          <template #suffix>
            <el-icon class="btn el-input__icon" @click="search"
              ><Search
            /></el-icon>
          </template>
        </el-input>
      </span>
    </span>
  </span>
  <div class="line"></div>
</template>

<style scoped>
.highlight {
  color: var(--color-primary);
}
.switchContainer {
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: white;
  /* &::after {
    content: "";
    height: 1px;
    width: 100%;
    background-color: var(--color-line);
    position: absolute;
    bottom: 0;
  } */
  .btnGroup {
    display: flex;
    align-items: center;
    .option {
      width: 105px;
      /* 这个数字没有严丝合缝的遵循原型，因为font-weight变化时宽度会变，如果严格按照原型，文稿按钮会被顶的左右移动 */
      display: flex;
      align-items: center;
      margin-left: 30px;
      cursor: pointer;
      font-size: 16px;
      color: var(--color-grey);
      font-family: var(--font-family-text);
    }
    .active {
      color: var(--color-primary);
      font-weight: 600;
    }
  }
  .rightContent {
    overflow-x: hidden;
    padding-right: 30px;
    .input {
      width: 200px;
    }
    .btn {
      cursor: pointer;
    }
    &:deep(.el-input__wrapper) {
      --el-input-focus-border-color: var(--color-primary);
      border-radius: 0px;
    }
  }
}
.select-group {
  .select {
    /*--el-color-primary: var(--color-primary);*/
    width: 180px;

    &:deep(.el-input) {
      --el-input-height: 35px;
      line-height: 35px;
    }
  }
}
</style>
