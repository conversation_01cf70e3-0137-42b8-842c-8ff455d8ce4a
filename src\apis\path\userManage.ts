import { http } from '@/apis';
import type {APIResponse, userObj} from "@/utils/type";
export interface key2userList {
    current: number,
    keywords?: string,
    limit: number,
}
export function getUserList(param: key2userList): Promise<APIResponse>{
    return http.request({
        method: 'post',
        url: '/user/getAll',
        data: param
    });
}
export function editUserInfo(param: userObj): Promise<APIResponse>{
    return http.request({
        method: 'post',
        url: '/user/editAdmin',
        data: param
    });
}