<script setup lang="ts">
import {nextTick, onMounted, onUpdated, ref} from "vue";

const props = defineProps({
  type: {
    type: String,
    required: false,
    default: "target",
  },
  tagId: {
    type: String,
    required: true,
  },
  deletable: {
    type: Boolean,
    required: false,
    default: true,
  },
  ellipsis: {
    type: Boolean,
    required: false,
    default: false,
  }
});
const content = ref();
const emit = defineEmits(['delete']);
const handleDelete = () => {
  emit('delete', props.tagId);
}
</script>

<template>
  <span :class="props.type">
    <span class="text">
      <slot ref="content"></slot>
    </span>
    {{ content }}
    <span v-if="props.ellipsis">...</span>
    <span v-if="deletable" @click="handleDelete" class="btn">×</span>
  </span>
</template>

<style scoped>
.target {
  height: 35px;
  width: 120px;
  border: 1px solid var(--color-primary);
  background-color: white;
  color: var(--color-primary);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 10px;
  .btn {
    color: var(--color-grey);
  }
}
.tag {
  height: 25px;
  width: 120px;
  border-radius: 10px;
  background-color: #9eb7e5;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
}
.text {
  overflow: hidden;
  white-space: nowrap;
}
.btn {
  margin-left: 5px;
  cursor: pointer
}
</style>