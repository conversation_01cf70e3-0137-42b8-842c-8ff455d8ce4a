<script setup lang="ts">
import MyButton from "@/views/common/myButton.vue";
import {ElMessage, genFileId, type UploadProps, type UploadRawFile, type UploadRequestOptions} from "element-plus";
import {ref, watch} from "vue";
import {uploadLecture, clearLecture} from "@/apis/path/createProject";
import type {lectureType} from "@/utils/type";
import LectureLine from "@/views/createProject/components/lectureLine.vue";

const props = defineProps({
  projectId: {
    type: Number,
    required: true
  },
  sectionId: {
    type: Number,
    required: true
  },
  lecture: {
    type: Array as () => lectureType[],
    required: true
  }
});
const emits = defineEmits(['sendLecture','cleanLecture']);
const prjId = ref();
const secId = ref();
const isShowLectureLine = ref(false)
const upload = ref();
const lectureList = ref<lectureType[]>([]);
watch(() => props, (newValue, oldValue) => {
  // console.log('videoLecture: watched\n' + JSON.stringify(newValue, null, 2))
  prjId.value = props.projectId;
  secId.value = props.sectionId;
  lectureList.value = [...props.lecture];
},{deep: true, immediate: true})
const handleVideoBefore = (rawFile: UploadRawFile) => {
//   FIXME：.srt文件的类型打不出来，暂时用accept字段来限制类型了
}
const uploadText = (item: any) => {
  let formData = new FormData();
  formData.append("file", item.file);
  formData.append("sectionId",secId.value.toString());
  uploadLecture(formData).then(res => {
    if (res.success) {
      lectureList.value = [];
      lectureList.value.push(...res.data.list);
      ElMessage.success("上传成功");
      emits('sendLecture', lectureList.value);
    } else {
      ElMessage.error(res.message);
    }
  }).catch()
}

const handleExceed: UploadProps['onExceed'] = (files) => {
  upload.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  upload.value!.handleStart(file)
}

const handleChangeShow = () => {
  isShowLectureLine.value = !isShowLectureLine.value
}

const handleClearLecture = () => {
  clearLecture(secId.value).then(res => {
    if (res.success) {
      ElMessage.success('清空讲稿成功');
      upload.value!.clearFiles();
      lectureList.value = [];
      emits('cleanLecture',true)
    }
    else {
      ElMessage.error(res.message);
    }
  }).catch()
}
</script>

<template>
  <div class="lecture-wrapper">
    <div class="toolbar">
      <span class="left-toolbar">
      <el-upload
          ref="upload"
          class="upload"
          action=""
          :limit="1"
          :http-request="uploadText"
          :on-exceed="handleExceed"
          accept=".srt"
      >
        <!-- TODO：差个图标 -->
        <my-button style="margin-right: 10px">导入</my-button>
      </el-upload>
      <my-button @click="handleClearLecture" type="light">清空</my-button>
    </span>
      <span class="right-toolbar" v-if="lectureList.length > 0">
          <span class="change-show-btn" @click="handleChangeShow">
            {{ isShowLectureLine ? "折叠" : "展开" }}
          </span>
        </span>
    </div>
    <div class="lecture-container" v-if="isShowLectureLine">
      <lecture-line v-for="(lec, idx) in lectureList" :key="lec.id" :lecture="lec" :index="idx" :step="2"></lecture-line>
    </div>
    <div v-if="!isShowLectureLine && lectureList.length > 0" class="lecture-block">
      隐藏的文稿
    </div>
  </div>
</template>

<style scoped>
.lecture-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  .toolbar {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    .left-toolbar {
      display: flex;
      flex-direction: row;
    }
    .right-toolbar {
      
    }
  }
  .change-show-btn {
      font-size: 14px;
      color: var(--color-primary);
      cursor: pointer;
    }
  .lecture-container {
    display: flex;
    width: 100%;
    flex-direction: column;
  }
  .lecture-block {
    background-color: var(--color-light);
    padding: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>