<script setup lang="ts">
import {onMounted, ref} from "vue";
import {ElMessage} from "element-plus";
import myButton from "@/views/common/myButton.vue"
import {getMemberList4Administrator} from "@/apis/path/examineManage";

const isDialogShow = ref(false);
const characterId = ref(0);
const characterTitle = ref("");
const tableData = ref();
const getList = () => {
  getMemberList4Administrator(characterTitle.value).then(res => {
    if (res.success) {
      tableData.value = res.data.list;
    }
    else {
      ElMessage.error(res.message);
    }
  }).catch()
}
const showDialog = (id: number, title: string) => {
  characterId.value = id;
  characterTitle.value = title;
  isDialogShow.value = true;
  getList();
}
const handleSubmit = () => {
  handleClose();
}
const handleClose = () => {
  isDialogShow.value = false;
}
onMounted(() => {
});
defineExpose({
  showDialog
});
</script>

<template>
  <el-dialog
      :close-on-click-modal="false"
      v-model="isDialogShow"
      width="596"
  >
    <template #header="{titleId}">
      <div :id="titleId" class="title">
        成员列表
      </div>
    </template>
    <div class="content-container">
      <div class="table-title">
        {{characterTitle}}
      </div>
      <el-table :data="tableData" style="width: 100%" empty-text="暂无数据">
        <el-table-column type="index" label="序号" width="55" >
        </el-table-column>
<!--        <el-table-column prop="name" label="账号" width="235" >-->
<!--        </el-table-column>-->
<!--        <el-table-column prop="showName" label="显示名称" width="232" >-->
<!--        </el-table-column>-->
            <!--name那列合并给了showName-->
            <el-table-column prop="showName" label="显示名称" width="467" >
            </el-table-column>
      </el-table>
    </div>
    <template #footer>
      <div class="footer">
        <div class="btn-group">
          <my-button type="light" @click="handleClose">关闭</my-button>
          <my-button @click="handleSubmit">确认</my-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
/* el-dialog的渲染是通过teleport直接挂到body下边，在这里写样式不生效，所以把样式写到全局(App.vue)了 */
.title {
  font-size: 16px;
  font-family: var(--font-family-text);
}
.content-container {
  font-family: var(--font-family-text);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .table-title {
    display: flex;
    width: 100%;
    font-size: 14px;
    justify-content: flex-start;
    padding-bottom: 10px;
    /* &::after {
      content: "";
      position: absolute;
      bottom: 0px;
      width: 100%;
      height: 1px;
      background-color: var(--color-boxborder);
    } */
  }
  .el-table {
    &:deep(.el-table__cell) {
      padding: 0;
    }
    &:deep(.cell) {
      justify-content: center;
      display: flex;
      align-items: center;
      min-height: 46px;
    }
  }
}
.footer {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
  .btn-group {
    display: flex;
    justify-content: space-between;
    width: 280px;
  }
}
</style>