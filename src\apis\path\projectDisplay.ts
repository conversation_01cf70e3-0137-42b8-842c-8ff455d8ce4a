import { http } from '@/apis';
import type {APIResponse} from "@/utils/type";

export interface key2projectList {
    current: number,
    limit: number,
    prjForm: string,
    prjType?: string,
    releaseTime?: string,
    status?: string,
    title?: string
    sort?: number,
}
export function getProjectList(param: key2projectList): Promise<APIResponse> {
    return http.request({
        method: 'post',
        url: '/tprj/getAllPrj',
        data: param,
    });
}