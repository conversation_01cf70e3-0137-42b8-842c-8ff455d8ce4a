<script setup lang="ts">
import { nextTick, reactive, ref } from "vue";
import {
  examType,
  examTypeDict,
  qMode,
  qModeDict,
  qType,
  qTypeDict,
} from "@/utils/constant";
import type { examSectionType, questionType, tagType } from "@/utils/type";
import { findKeyByValue } from "@/utils/func";
import MyButton from "@/views/common/myButton.vue";
import { ElMessage } from "element-plus";
import MyTag from "@/views/common/myTag.vue";
import AddTDialog from "@/views/createProject/step1/addTDialog.vue";
import type { FormInstance, FormRules } from "element-plus";
import editor from "@/components/editors/VditorInline.vue";
// const props = defineProps({
//   question: {
//     type: Object as () => questionType,
//     required: true,
//   }
// });
const emits = defineEmits(["submit", "open", "close"]);
const isDialogShow = ref(false);
const dialogRef = ref();
const dialogTitle = ref();
const editorRef = ref();
const curQuestion = reactive<questionType>({
  qId: -1,
  relatedText: "",
  qContent: "",
  qType: qType.what, // 是什么 | 为什么
  qMode: qMode.ness, // 必要 | 参考
  klg: [],
  explanation: "",
});
const displayQuestionList = ref<questionType[]>();
const ruleFormRef = ref<FormInstance>();
const rules = reactive<FormRules<questionType>>({
  qContent: [{ required: true, message: "请输入关键字", trigger: "blur" }],
  qType: [{ required: true, message: "请选择问题类型", trigger: "change" }],
  qMode: [{ required: true, message: "请选择问题属性", trigger: "change" }],
  klg: [{ required: false, message: "请选择知识点", trigger: "change" }],
  explanation: [
    { required: false, message: "请输入问题解析", trigger: "blur" },
  ],
});
const curMode = ref<number>(); // 0新增|1编辑|2只读
const showDialog = (questionList: questionType[], mode: number) => {
  displayQuestionList.value = questionList;
  curMode.value = mode; // 2:不可操作
  Object.assign(curQuestion, questionList[0]);
  console.log("newQuestion: " + JSON.stringify(questionList, null, 2));
  console.log("curQuestion: " + JSON.stringify(curQuestion, null, 2));
  switch (curMode.value) {
    case 0:
      dialogTitle.value = "添加问题";
      break;
    case 1:
      dialogTitle.value = "编辑问题";
      break;
    case 2:
      dialogTitle.value = "展示问题";
      break;
    default:
      console.log("问题弹窗mode异常");
      break;
  }
  isDialogShow.value = true;
  nextTick(() => {
    editorRef.value.setData(curQuestion.relatedText, curMode.value == 2);
  });
  emits("open");
};
const handleChangeQType = (value: number) => {
  curQuestion.qType = value;
  if (value == qType.what) {
    curQuestion.klg = [curQuestion.klg[0]];
  }
};
const handleDelete = (aimId: string) => {
  const newKlg: tagType[] = curQuestion.klg.filter((item) => item.id != aimId);
  curQuestion.klg = newKlg;
};
const handleClose = () => {
  window.getSelection()?.empty();
  isDialogShow.value = false;
  emits("close");
};
const handleSubmit = () => {
  curQuestion.qContent = editorRef.value.getData();
  if (!ruleFormRef.value) return;
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      console.log("submit: " + JSON.stringify(curQuestion, null, 2));
      emits("submit", curQuestion);
      handleClose();
    } else {
      console.log("error submit!");
      return false;
    }
  });
};
const handleOpenTargetDialog = () => {
  if (curMode.value == 2) {
    return;
  }
  if (curQuestion.qType == qType.what) {
    dialogRef.value.showDialog("target", 3, 1, curQuestion.klg);
  } else {
    dialogRef.value.showDialog("target", 3, -1, curQuestion.klg);
  }
};
const handleChangeQuestion = (newQuestion: questionType) => {
  Object.assign(curQuestion, newQuestion);
};
const receiveTarget = (tList: tagType[], tForm: string) => {
  if (tForm == "target") {
    curQuestion.klg = tList;
    // console.log('receive klgs: ', curQuestion.value.klg);
  } else {
    console.log("选择知识点异常");
  }
};
defineExpose({
  showDialog,
});
</script>

<template>
  <el-dialog
    @close="handleClose"
    class="question-dialog"
    v-model="isDialogShow"
    :close-on-click-modal="false"
    width="596"
  >
    <template #header="{ titleId }">
      <div :id="titleId" class="title">
        {{ dialogTitle }}
      </div>
    </template>
    <el-form ref="ruleFormRef" :model="curQuestion" :rules="rules">
      <div class="content-container">
        <div class="tip">问题内容</div>
        <div class="related-content-container">
          文本关联内容：
          <b class="ck-content" v-html="curQuestion.relatedText"></b>
        </div>
        <el-form-item prop="qContent">
          <editor ref="editorRef"></editor>
        </el-form-item>
        <el-form-item prop="qType">
          <el-select
            :disabled="curMode == 2"
            v-model="curQuestion.qType"
            @change="handleChangeQType"
            style="margin-bottom: 10px"
          >
            <el-option
              v-for="(value, key) in qTypeDict"
              :key="key"
              :label="key"
              :value="value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="qMode">
          <el-select
            :disabled="curMode == 2"
            v-model="curQuestion.qMode"
            style="margin-bottom: 20px"
          >
            <el-option
              v-for="(value, key) in qModeDict"
              :key="key"
              :label="key"
              :value="value"
            ></el-option>
          </el-select>
        </el-form-item>
        <div
          style="
            height: 1px;
            background-color: var(--color-boxborder);
            margin-bottom: 10px;
          "
        ></div>
        <div style="margin-bottom: 10px">问题解答</div>
        <el-form-item prop="klg">
          <div class="klg-wrapper">
            <my-button
              v-show="curMode != 2"
              style="margin-right: 30px"
              @click="handleOpenTargetDialog"
            >
              + 添加知识
            </my-button>
            <div
              class="selectedTList"
              id="bottom"
              v-if="curQuestion.klg.length > 0"
            >
              <my-tag
                class="t"
                :deletable="curMode != 2"
                @delete="handleDelete"
                type="target"
                v-for="item in curQuestion.klg"
                :tag-id="item.id"
                :key="item.id"
              >
                <el-tooltip
                  popper-class="tooltip-width"
                  :content="item.name"
                  raw-content
                >
                  <span class="ck-content" v-html="item.name"></span>
                  <!--                {{item.name}}-->
                </el-tooltip>
              </my-tag>
            </div>
          </div>
        </el-form-item>
        <el-form-item prop="explanation">
          <el-input
            :disabled="curMode == 2"
            v-model="curQuestion.explanation"
            type="textarea"
            placeholder="请输入说明内容"
          ></el-input>
        </el-form-item>
      </div>
    </el-form>
    <template #footer>
      <div class="footer">
        <div v-if="curMode != 2" class="btn-group">
          <my-button type="light" @click="handleClose">关闭</my-button>
          <my-button @click="handleSubmit">确认</my-button>
        </div>
        <div v-else class="pagination">
          <div
            class="btn"
            :class="curQuestion.qId == q.qId ? 'focused' : ''"
            v-for="(q, idx) in displayQuestionList"
            :key="q.qId"
            @click="handleChangeQuestion(q)"
          ></div>
        </div>
      </div>
    </template>
  </el-dialog>
  <add-t-dialog ref="dialogRef" @submit="receiveTarget"></add-t-dialog>
</template>

<style scoped>
/* el-dialog的渲染是通过teleport直接挂到body下边，在这里写样式不生效，所以把样式写到全局(App.vue)了 */
.el-select-dropdown__item {
  color: var(--color-black);
  font-family: var(--font-family-text);

  &:hover * {
    font-weight: 600;
  }

  &.selected {
    color: var(--color-primary);
  }
}

.title {
  font-size: 16px;
  font-family: var(--font-family-text);
}

.content-container {
  display: flex;
  flex-direction: column;

  :deep(.is-disabled .el-textarea__inner),
  :deep(.is-disabled .el-input__wrapper) {
    background-color: white;
    resize: none;
    /*禁止文本域拖拽事件*/
  }

  .el-select {
    width: 100%;
  }

  .tip {
    margin-bottom: 6px;
  }

  .related-content-container {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    height: 35px;
    padding: 0 15px;
    background-color: var(--color-light);
  }

  .klg-wrapper {
    width: 100%;
    display: flex;
    flex-direction: row;
    margin-bottom: 10px;
  }

  .selectedTList {
    width: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    overflow: auto;

    .t {
      margin: 0 10px 10px 0;
    }
  }
}

.footer {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-top: 20px;

  .btn-group {
    display: flex;
    justify-content: space-between;
    width: 280px;
  }

  .pagination {
    display: flex;
    flex-direction: row;

    .btn {
      cursor: pointer;
      margin: 0 5px;
      display: flex;
      width: 10px;
      height: 10px;
      border-radius: 5px;
      background-color: var(--color-grey);

      &.focused,
      &:hover {
        background-color: var(--color-primary);
      }
    }
  }
}
</style>
