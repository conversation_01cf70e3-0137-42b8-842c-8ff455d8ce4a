<script setup lang="ts">
import MyButton from "@/views/common/myButton.vue";
import {onMounted, ref} from "vue";
import {useRouter} from "vue-router";
import {editingPrjStore} from "@/stores/editingPrj";
import {createProject} from "@/apis/path/createProject";
import {ElMessage} from "element-plus";

const editingPrj = editingPrjStore();
const router = useRouter()
const curForm = ref(1);
const handleConcel = () => {
  window.close()
}
const changeForm = (newForm: number) => {
  curForm.value = newForm;
}
const handleNextStep = () => {
  router.push({
    path: '/home/<USER>/step1',
    query: {
      prjForm: curForm.value
    }
  });
}
onMounted(() => {
})
</script>

<template>
  <div class="main-container">
    <!--    <el-radio-group v-model="curForm" class="ml-4">-->
    <span class="card-group">
      <span class="card" :class="{ 'card-select': curForm === 1 }" @click="changeForm(1)">
        <div class="head">
          <img src="../../../assets/images/create/u3454.svg">
          视频项目
          <div class="line" style="margin-top: 14px;"></div>
        </div>
        <div class="content">
          <div style="margin-bottom: 22px">
            &nbsp;&nbsp;&nbsp;&nbsp;视频项目以视频为主要内容，包含一段或多段视频内容，并辅以文字讲稿。
          </div>
          &nbsp;&nbsp;&nbsp;&nbsp;创建视频项目之前，请准备项目和视频的封面图片、视频内容和对应的带时间轴的文字讲稿。
        </div>
        <div class="select">
          <!-- element-plus 2.6.0 以后的版本新增了value这个api用于传值，并且计划在3.0.0移除label这个api，如果我们的系统升级了组件库，这个地方要注意一下-->
          <el-radio v-model="curForm" :label="1">选我</el-radio>
        </div>
      </span>
      <span class="card" :class="{ 'card-select': curForm === 2 }" @click="changeForm(2)">
        <div class="head">
          <img src="../../../assets/images/create/u3462.svg">
          文稿项目
          <div class="line" style="margin-top: 14px;"></div>
        </div>
        <div class="content">
          <div style="margin-bottom: 22px">
            &nbsp;&nbsp;&nbsp;&nbsp;文稿项目以文字、图片和表格等为主要形式。
          </div>
          &nbsp;&nbsp;&nbsp;&nbsp;创建文稿项目，需要准备封面图片，文稿内容。
        </div>
        <div class="select">
          <!-- element-plus 2.6.0 以后的版本新增了value这个api用于传值，并且计划在3.0.0移除label这个api，如果我们的系统升级了组件库，这个地方要注意一下-->
          <el-radio v-model="curForm" :label="2">选我</el-radio>
        </div>
      </span>
    </span>
    <!--    </el-radio-group>-->
    <span class="button-group">
      <my-button type="light" @click="handleConcel()">取消</my-button>
      <my-button @click="handleNextStep">下一步</my-button>
    </span>
  </div>
</template>

<style scoped>
.main-container {
  font-family: var(--font-family-text);
  background-color: white;
  color: var(--color-black);
  width: var(--width-content);
  /* margin: 20px auto auto auto; */
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 100px;
  .card-group {
    width: 640px;
    display: flex;
    justify-content: space-between;
    margin-bottom: 60px;

    .card {
      width: 300px;
      height: 339px;
      border-radius: 5px;
      border: 1px solid var(--color-boxborder);
      padding: 40px 30px 10px 30px;
      font-family: var(--font-family-text);
      cursor: pointer;
      .head {
        font-family: var(--font-family-title);
        color: var(--color-primary);
        font-size: 18px;
        padding-bottom: 17px;
        text-align: center;
        /* &::after {
          content: "";
          position: absolute;
          left: 0;
          bottom: 0;
          width: 100%;
          height: 1px;
          background-color: var(--color-boxborder);
        } */
      }
      .content {
        height: 200px;
        font-size: 16px;
        color: var(--color-black);
        /*margin-bottom: 20px;*/
      }
    }    
    .card-select {
      background-color: #F9F9F9;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    }
  }
  .button-group {
    width: 280px;
    display: flex;
    justify-content: space-between;
    margin-bottom: 60px;
  }
}
</style>