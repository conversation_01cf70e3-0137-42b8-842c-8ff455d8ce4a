<script setup lang="ts">
import MyButton from "@/views/common/myButton.vue";
import { inject, type Ref, ref, watch, computed } from "vue";
import {
  type UploadRequestOptions,
  type UploadRawFile,
  ElMessage,
  ElMessageBox,
  type UploadProps,
  genFileId,
} from "element-plus";
import {
  sliceUpload,
  buildKey,
  setPrjId4cosKey,
  getVideoCut,
  getVideoInfo,
  type UploadTask,
} from "@/utils/cos";
import { uploadVideo2Backend } from "@/apis/path/cosBackend";
import type { taskType } from "@/utils/type";
// import MyVideoPlayer from "@/views/common/myVideoPlayer.vue"
import { useUploadStore } from "@/stores/upload";
const uploadStore = useUploadStore();
const emits = defineEmits(["sendVideoKey", "isVideoUploadDone"]);
const props = defineProps({
  projectId: {
    type: Number,
    required: true,
  },
  sectionId: {
    type: Number,
    required: true,
  },
  releaseDate: {
    type: String,
    required: true,
  },
  videoKey: {
    type: String,
    required: true,
  },
});
const percentage = ref(0);
const isProgressShow = ref(false);
const upload = ref();
const taskList = inject("taskList") as Ref<taskType[]>;
// const isUploading = inject('uploading') as Ref;
const taskKey = ref();
const taskId = ref();
const videoSrc = ref();
const imgUrlList = ref<any[]>([]);
const videoDuration = ref(1);
const videoName = ref();
const videoPlayerRef = ref();
// 取消上传相关状态
const isCancelling = ref(false);
const showCancelBtn = ref(false);
// 当前上传任务引用
const currentUploadTask = ref<UploadTask | null>(null);
// 是否曾经有过上传记录（用于判断显示"上传"还是"重新上传"）
const hasUploadHistory = ref(false);
const form = ref({
  videoSrc: "",
});
const rules = ref({
  videoSrc: [{ required: true, message: "请上传视频", trigger: "blur" }],
});

// 计算属性：是否显示取消按钮
const shouldShowCancelBtn = computed(() => {
  return isProgressShow.value && percentage.value < 100 && !isCancelling.value;
});

// 计算属性：是否显示上传按钮
const shouldShowUploadBtn = computed(() => {
  return !isProgressShow.value || percentage.value >= 100;
});

// 清理上传任务状态
const cleanupUploadTask = (keepHistory = true) => {
  // 从任务列表中移除当前任务
  const index = taskList.value.findIndex(
    (task) => task.secId == props.sectionId
  );
  if (index !== -1) {
    taskList.value.splice(index, 1);
  }

  // 重置状态
  taskId.value = undefined;
  taskKey.value = undefined;
  percentage.value = 0;
  isProgressShow.value = false;
  showCancelBtn.value = false;
  currentUploadTask.value = null;

  // 如果不保留历史记录，则重置hasUploadHistory
  if (!keepHistory) {
    hasUploadHistory.value = false;
  }

  // 启用上传按钮
  uploadStore.setCanUpload(true);
};

// 取消上传
const cancelUpload = async () => {
  try {
    isCancelling.value = true;

    // 1. 首先中断客户端上传任务
    if (currentUploadTask.value) {
      currentUploadTask.value.cancel();
      console.log("已中断客户端上传任务");
    }

    // 2. 清理本地状态
    cleanupUploadTask();
  } catch (error) {
    console.error("取消上传异常:", error);
    ElMessage.error("取消上传失败");
    // 即使API调用失败，也要清理本地状态
    cleanupUploadTask();
  } finally {
    isCancelling.value = false;
  }
};

// 处理取消按钮点击
const handleCancelClick = () => {
  ElMessageBox.confirm("确定要取消上传吗？已上传的进度将会丢失。", "取消上传", {
    confirmButtonText: "确定取消",
    cancelButtonText: "继续上传",
    type: "warning",
  })
    .then(() => {
      cancelUpload();
    })
    .catch(() => {
      // 用户选择继续上传，不做任何操作
    });
};

const uploadVideo = async (file: UploadRawFile) => {
  videoSrc.value = undefined;
  const index = taskList.value.findIndex(
    (task) => task.secId == props.sectionId
  );
  if (index != -1) {
    // 本小节有任务，先清理
    cleanupUploadTask();
  } else {
    const secTask: taskType = {
      secId: props.sectionId,
      // 以下全都是ref传给cos，指针修改的
      taskId: taskId.value,
      taskKey: taskKey.value,
      taskProcess: percentage,
    };
    taskList.value.push(secTask);
  }

  // 显示进度条和取消按钮
  isProgressShow.value = true;
  showCancelBtn.value = true;
  // 标记有上传历史
  hasUploadHistory.value = true;

  console.log("videoUploader - releaseDate:", props.releaseDate);
  let key = buildKey(
    props.releaseDate,
    props.projectId,
    props.sectionId,
    "video",
    file.name
  );
  setPrjId4cosKey(props.projectId);
  try {
    // 创建上传任务并保存引用
    const uploadTask = sliceUpload(file, key, percentage, taskId, taskKey);
    currentUploadTask.value = uploadTask;

    // 使用Promise处理上传结果
    uploadTask.promise
      .then((res) => {
        // 上传完成，隐藏取消按钮
        showCancelBtn.value = false;
        currentUploadTask.value = null;

        uploadVideo2Backend(props.sectionId, key).then((res) => {
          if (res.success) {
            console.log("视频的key值成功传给后端");
            getVideoName(key);
          } else {
            ElMessage.error("上传失败");
          }
        });
        emits("sendVideoKey", key);
      })
      .catch((error: any) => {
        // 上传失败或被取消
        if (error.message !== "USER_CANCELLED") {
          ElMessage.error("上传失败");
        }
        cleanupUploadTask();
      });
  } catch (err) {
    console.log("error", err);
    cleanupUploadTask();
  }
};

const getVideoName = (key: string) => {
  const regex = /_(.*)/;
  const match = key.match(regex);
  if (match) {
    videoName.value = match[0].substring(1); // 从匹配的字符串中去掉第一个下划线
  } else {
    console.log("No underscore found");
  }
};
const handleUpload = (options: UploadRequestOptions) => {
  const file = options.file;
  uploadVideo(file);
};
const handleVideoBefore = (rawFile: UploadRawFile) => {
  uploadStore.setCanUpload(false);
  const ifLarge = rawFile.size / 1024 / 1024 >= 500;
  const ifNotVideo =
    rawFile.type !== "video/mp4" &&
    rawFile.type !== "video/mov" &&
    rawFile.type !== "video/wmv" &&
    rawFile.type !== "video.avi";
  if (ifLarge) ElMessage.error("视频大小不要超过500M");
  if (ifNotVideo) ElMessage.error("请传入支持的视频格式(mp4/mov/wmv/avi)");
  return !(ifLarge || ifNotVideo);
};
const handleExceed: UploadProps["onExceed"] = (files) => {
  isProgressShow.value = false;
  upload.value!.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  uploadVideo(file);
};

const generateRandomTick = (duration: number, n: number) => {
  const randomTicks = [];
  randomTicks.push(0.1);
  for (let i = 0; i < n - 1; i++) {
    // Math.random() 生成 0 到 1 之间的随机数
    // 乘以 (x - 0.1) 将范围扩展到 0.1 到 x-0.1
    // 加上 0.1 将范围最终调整到 0.1 到 x
    const randomTick = Math.random() * (duration - 0.1) + 0.1;
    randomTicks.push(randomTick);
  }
  randomTicks.sort((a, b) => a - b);
  console.log("randomTicks", randomTicks);
  return randomTicks;
};

const getVideoCutsFromCos = (key: string, num: number) => {
  const tickList = generateRandomTick(videoDuration.value, num);
  const tempUrlList = ref<any[]>([]);
  setPrjId4cosKey(props.projectId);
  for (let i = 0; i < num; i++) {
    getVideoCut(key, tickList[i]).then((result) => {
      tempUrlList.value.push(URL.createObjectURL(result.Body as Blob));
    });
  }
  imgUrlList.value = tempUrlList.value;
};
// 文件上传onchange事件
const onChange = (file: any) => {
  var url = URL.createObjectURL(file.raw); //获取录音时长
  var audioElement = new Audio(url); //audio也可获取视频的时长
  audioElement.addEventListener("loadedmetadata", function (_event) {
    videoDuration.value = audioElement.duration;
  });
};
watch(
  () => percentage.value,
  (newVal, oldVal) => {
    if (newVal >= 100) {
      uploadStore.setCanUpload(true);
      getVideoCutsFromCos(taskKey.value, 5);
    }
  }
);

watch(
  () => props,
  (newVal, oldVal) => {
    if (newVal.videoKey) {
      getVideoName(newVal.videoKey);
      setPrjId4cosKey(newVal.projectId);
      // 如果有videoKey，说明已经有上传记录
      hasUploadHistory.value = true;
      getVideoInfo(newVal.videoKey).then((res) => {
        if (res.Response.MediaInfo.Format.Duration) {
          videoDuration.value = res.Response.MediaInfo.Format.Duration;
          taskKey.value = newVal.videoKey;
          percentage.value = 100;
        }
      });
    }
  },
  { deep: true, immediate: true }
);
</script>

<template>
  <div class="upload-wrapper">
    <!-- {{ videoSrc }} -->
    <el-form :model="form" :rules="rules" ref="formRef" v-if="videoName">
      <el-form-item prop="videoUrl" style="width: 500px">
        <el-carousel indicator-position="outside" style="width: 500px">
          <el-carousel-item v-for="item in imgUrlList" :key="item">
            <img :src="item" alt="fuck" style="width: 500px; height: 100%" />
            <!-- <span>{{ item }}</span> -->
          </el-carousel-item>
        </el-carousel>
      </el-form-item>
    </el-form>
    <div class="right-wrapper">
      <span style="margin-bottom: 20px">{{ videoName }}</span>
      <!-- 上传按钮 - 只在非上传状态时显示 -->
      <el-upload
        v-if="shouldShowUploadBtn"
        ref="upload"
        class="upload"
        action=""
        :limit="1"
        :show-file-list="false"
        :on-exceed="handleExceed"
        :on-change="onChange"
        :http-request="handleUpload"
        :before-upload="handleVideoBefore"
        :disabled="!uploadStore.canUpload"
        accept=".mp4,.mov,.wmv,.avi"
      >
        <!-- TODO：差个图标 -->
        <my-button :clickable="uploadStore.canUpload">{{
          hasUploadHistory ? "重新上传" : "上传视频"
        }}</my-button>
      </el-upload>

      <!-- 取消上传按钮 - 只在上传过程中显示 -->
      <my-button
        v-if="shouldShowCancelBtn"
        :clickable="!isCancelling"
        @click="handleCancelClick"
        class="cancel-btn"
        style="background-color: #f56c6c; border-color: #f56c6c"
      >
        {{ isCancelling ? "取消中..." : "取消上传" }}
      </my-button>

      <div class="tip">
        支持上传文件最大1G，支持上传是视频格式为mp4,avi,wmv,mov
      </div>
      <div class="progress" v-show="isProgressShow">
        <el-progress :percentage="percentage" color="var(--color-primary)" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.upload-wrapper {
  width: 100%;
  margin-bottom: 20px;
  padding: 30px 0;
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  text-align: center;
  align-items: center;
  border: 2px dashed var(--color-primary);

  .left-wrapper {
    display: flex;
    width: 640px;
    height: 360px;
  }

  .right-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    max-width: 500px;

    .upload {
      :deep(.el-upload) {
      }
    }

    .cancel-btn {
      :deep(.el-button) {
        background-color: #f56c6c !important;
        border-color: #f56c6c !important;
        color: white !important;

        &:hover {
          background-color: #f78989 !important;
          border-color: #f78989 !important;
        }

        &:disabled {
          background-color: #f5f5f5 !important;
          border-color: #e4e7ed !important;
          color: #c0c4cc !important;
        }
      }
    }

    .tip {
      font-size: 12px;
      color: var(--color-grey);
    }

    .progress {
      width: 80%;
    }
  }
}
</style>
