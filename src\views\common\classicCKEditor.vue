<script setup lang="ts">
import {
  computed,
  inject,
  nextTick,
  onBeforeUnmount,
  onMounted,
  ref,
  watch,
} from "vue"
import MyUploadAdapter from "@/utils/ckEditorImgUploader"
import { defineModel, defineProps } from "vue"
import { autoSave, listenSave, removeListenSave } from "@/utils/ckListenerFunc"
const Editor = inject("Editor") as any

const props = defineProps({
  disabled: {
    type: Boolean,
    default: false,
  },
  height: {
    type: Number,
    default: 300,
  },
  focusSave: {
    type: Boolean,
    default: false,
  },
  saveCallBack: {
    type: Function,
    default: () => {
      console.log("save-call-back Function LOCK")
    },
  },
})

const editorConfig = {
  placeholder: "请输入",
  extraPlugins: [MyCustomUploadAdapterPlugin],
}
const model = defineModel()
const focusing = ref(false)
let timer: number | null | undefined
let timeoutToStop: number | undefined
function MyCustomUploadAdapterPlugin(editor) {
  editor.plugins.get("FileRepository").createUploadAdapter = (loader) => {
    return new MyUploadAdapter(loader)
  }
}
const handleOnFocus = () => {
  console.log("onfocus")
  if (!props.focusSave) return
  focusing.value = true
  listenSave(document, props.saveCallBack)
}
const handleOnBlur = () => {
  console.log("onblur")
  if (!props.focusSave) return
  focusing.value = false
  stopTimer()
  removeListenSave(document)
}
// 开始轮询保存
const startTimer = () => {
  timer = setInterval(() => {
    console.log("该保存了！")
    autoSave(props.saveCallBack, 100)
  }, 5000)
}
// 停止轮询保存
const stopTimer = () => {
  if (timer) {
    clearInterval(timer as number)
    clearTimeout(timeoutToStop)
    timer = null
  }
}
// 延时轮询保存
const timeoutToStopTimer = () => {
  if (timer) {
    clearTimeout(timeoutToStop)
  }
  timeoutToStop = setTimeout(() => {
    console.log("超时关闭定时器")
    stopTimer()
  }, 10000)
}
watch(
  () => model.value,
  (oldValue, newValue) => {
    if (oldValue !== newValue) {
      nextTick(() => {
        if (timer) {
          timeoutToStopTimer()
        }
      })
      if (!timer && focusing.value) startTimer()
    }
  }
)
onBeforeUnmount(() => {
  stopTimer()
})
onMounted(() => {
  document.documentElement.style.setProperty("--ck-height", `${props.height}px`)
})
</script>

<template>
  <div>
    <ckeditor
      :editor="Editor.ClassicEditorCustom"
      v-model="model"
      :config="editorConfig"
      class="classicEditor"
      :disabled="props.disabled"
      @focus="handleOnFocus"
      @blur="handleOnBlur"
    >
    </ckeditor>
  </div>
</template>

<style scoped>
.ck.ck-editor__editable_inline > :last-child {
  margin-bottom: 0px;
}

.ck.ck-editor__editable_inline > :first-child {
  margin-top: 0px;
}
</style>
<style>
.ck.ck-editor__main > .ck-editor__editable {
  background: var(--ck-color-base-background);
  min-height: var(--ck-height);
}
</style>
