<script setup lang="ts">
import FormSwitch from "@/views/common/formSwitch.vue";
import myFlipper from "@/views/common/myFlipper.vue";
import {
  formDict,
  prjForm,
  prjState,
  prjType,
  sortDict,
  typeDict,
} from "@/utils/constant";
import { useRouter } from "vue-router";
import { nextTick, onMounted, onUpdated, ref, watchEffect } from "vue";
import type { prjInfo2_4table } from "@/utils/type";
import { decodeData } from "@/utils/func";
import { ElMessage } from "element-plus";
import {
  getProjectList,
  type key2projectList,
} from "@/apis/path/projectDisplay";
import { renderMarkdown } from "@/utils/markdown";

const router = useRouter();
const prjTable = ref();
const curForm = ref("");
const curState = ref<prjState>(prjState.default);
const curType = ref<prjType>(prjType.default);
const curTime = ref("");
const curSort = ref(0);
const tableData = ref();
const searchKey = ref("");
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const handleSelect = (form: prjForm) => {
  curForm.value = form == 3 ? "" : form.toString();
  getPrjList();
};
const handleSearch = (key: string) => {
  // console.log(key);
  searchKey.value = key;
  getPrjList();
};

const getPrjList = () => {
  const param = ref<key2projectList>({
    current: currentPage.value,
    limit: pageSize.value,
    prjForm: curForm.value.toString(),
    prjType: curType.value.toString(),
    releaseTime: curTime.value,
    status: curState.value.toString(),
    title: searchKey.value,
    sort: curSort.value,
  });
  // console.log("[debug]",param.value);
  getProjectList(param.value)
    .then((res) => {
      if (res.success) {
        // tagName: 这个地方，decode里边是识别prtype的，但是目前这个结构里的类型叫prjType，后端统一字段后，所有问题就迎刃而解了
        tableData.value = res.data.list?.map((prj: prjInfo2_4table) =>
          decodeData(prj)
        );
        total.value = res.data.total;
      } else {
        ElMessage.error(res.message);
      }
    })
    .catch();
};
const ToPreview = (id: number, form: string) => {
  const { href } = router.resolve({
    path: "/home/<USER>",
    query: {
      prjId: id,
      prjForm: formDict[form],
    },
  });
  window.open(href, "_blank");
};
const handleChangePage = (newPage: number) => {
  currentPage.value = newPage;
  getPrjList();
};
const indexMethod = (index: number) => {
  return (currentPage.value - 1) * pageSize.value + index + 1;
};
onMounted(() => {
  getPrjList();
  document.addEventListener("visibilitychange", () => {
    if (!document["hidden"]) {
      getPrjList();
      // console.log('出现');
    } else {
      //隐藏
      // console.log('隐藏');
    }
  });
});
const setCur = (cur: any) => {
  curType.value = cur.curType;
  curTime.value = cur.curTime;
  curSort.value = cur.curSort;
  getPrjList();
};
</script>

<template>
  <div class="main-container">
    <form-switch
      :need-cur-time="true"
      :need-cur-sort="true"
      @search="handleSearch"
      @select="handleSelect"
      @cur="setCur($event)"
      placeholder="请输入项目标题"
    >
    </form-switch>
    <div class="content-container">
      <div class="line"></div>
      <el-table
        ref="prjTable"
        :data="tableData"
        style="width: 100%"
        empty-text="暂无数据"
      >
        <el-table-column
          type="index"
          :index="indexMethod"
          label="序号"
          width="89"
        >
        </el-table-column>
        <el-table-column
          class="notFixWidth"
          prop="title"
          label="项目名称"
          width="424"
        >
          <template #default="scope">
            <span class="title-wrapper">
              <span
                class="title prname ck-content"
                @click="ToPreview(scope.row.oId, scope.row.prform)"
                v-html="renderMarkdown(scope.row.title)"
              ></span>
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="prform" label="项目形式" width="148">
        </el-table-column>
        <el-table-column
          prop="prtype"
          label="项目类型"
          width="143"
        ></el-table-column>
        <el-table-column prop="name" label="作者" width="152"></el-table-column>
        <el-table-column
          prop="releaseTime"
          label="发布日期"
          width="184"
        ></el-table-column>
      </el-table>
      <my-flipper
        @change-page="handleChangePage"
        :current="currentPage"
        :page-size="pageSize"
        :total="total"
      ></my-flipper>
    </div>
  </div>
</template>

<style scoped>
.main-container {
  font-family: var(--font-family-text);
  color: var(--color-black);
  width: var(--width-content);
  /* margin: 20px auto auto auto; */
  .content-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 20px 30px 30px 30px;
    background-color: white;
    .toolbar {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      flex-direction: row;
      padding-bottom: 20px;
      .select-group {
        width: 620px;
        display: flex;
        justify-content: space-between;
        .select {
          /*--el-color-primary: var(--color-primary);*/
          width: 180px;
          &:deep(.el-input) {
            --el-input-height: 35px;
            line-height: 35px;
          }
        }
      }
      /* &::after {
        content: '';
        height: 1px;
        width: 100%;
        background-color: var(--color-line);
        position: absolute;
        bottom: 0;
      } */
    }
    .el-table {
      &:deep(.el-table__cell) {
        padding: 0;
      }
      &:deep(.cell) {
        justify-content: center;
        display: flex;
        align-items: center;
        min-height: 46px;
      }
    }
    .title-wrapper {
      width: 100%;
      justify-content: flex-start;
      .title:hover {
        cursor: pointer;
        color: var(--color-primary);
        text-decoration: underline;
      }
    }
  }
}
:deep(.el-date-table td.today .el-date-table-cell__text) {
  color: var(--color-primary);
}
</style>
