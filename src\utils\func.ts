import {
  prjForm,
  prjType,
  typeDict_noDefault,
  stateDict,
} from "@/utils/constant";
import type { prjInfoType, tagType } from "@/utils/type";
export const findKeyByValue = (
  value: string | number,
  dict: { [key: string]: string } | { [key: string]: number }
) => Object.keys(dict).find((key) => dict[key] === value);
export const decodeData = (data: any) => {
  // console.log("===>"+JSON.stringify(data));
  if (data.prform == null) data.prform = "暂无数据";
  else
    switch (data.prform) {
      case prjForm.video:
        data.prform = "视频";
        break;
      case prjForm.text:
        data.prform = "文稿";
        break;
      default:
        data.prform = "暂无数据";
        break;
    }
    if (data.prtype == null) data.prtype = '暂无数据';
    else switch (data.prtype.toString()) {
        case prjType.klg:
            data.prtype = "知识讲解";
            break;
        case prjType.prj:
            data.prtype = "案例学习";
            break;
        case prjType.exam:
            data.prtype = "测评学习";
            break;
        case prjType.area:
            data.prtype = "领域讲解";
            break;
        default: data.prtype = '暂无数据';
            break;
    }
  // if (data.approvalAuthority == null) data.approvalAuthority = '暂无数据';
  // else switch (data.approvalAuthority.toString()) {
  //     case 1 :
  //         data.approvalAuthority = '';
  //         break;
  //     case 2 :
  //         data.approvalAuthority = '';
  //         break;
  //     case 3 :
  //         data.approvalAuthority = '';
  //         break;
  //     default: data.prtype = '暂无数据';
  //         break;
  // }
  // switch (data.condition.toString()) {
  //   case prjState.draft:
  //     data.condition = "草稿";
  //     break;
  //   case prjState.wait2examine:
  //     data.condition = "待审核";
  //     break;
  //   case prjState.examining:
  //     data.condition = "审核中";
  //     break;
  //   case prjState.back:
  //     data.condition = "已退回";
  //     break;
  //   case prjState.withdraw:
  //     data.condition = "已撤回";
  //     break;
  //   case prjState.publish:
  //     data.condition = "已发布";
  //     break;
  //   case prjState.delete:
  //     data.condition = "已删除";
  //     break;
  //   default: data.condition = '解析失败';
  //     break;
  // }
  return data;
};
export const decodeData2 = (data: any) => {
  for (const key in data) {
    data[key] = data[key] !== null || undefined ? data[key] : "暂无数据";
  }
  return data;
};
export const pushData = (data: any) => {
  if (data.prjForm == null) data.prjForm = "暂无数据";
  else
    switch (data.prjForm.toString()) {
      case prjForm.video:
        data.prform = "视频";
        break;
      case prjForm.text:
        data.prform = "文稿";
        break;
      default:
        data.prform = "暂无数据";
        break;
    }
  //这里改成枚举类会更好
  switch (data.status) {
    case 0:
      data.status = "草稿";
      break;
    case 1:
      data.status = "待审核";
      break;
    case 2:
      data.status = "审核中";
      break;
    case 3:
      data.status = "已退回";
      break;
    case 4:
      data.status = "已撤回";
      break;
    case 5:
      data.status = "已发布";
      break;
    case 6:
      data.status = "已删除";
      break;
    default:
      data.condition = "解析失败";
      break;
  }
  return data;
};

export const formatData_step1 = (data: any) => {
  console.log("-->start format data in step1");
  const formatData: prjInfoType = {
    disable: true,
    prjType: "",
    prjName: "",
    prjAim: "",
    prjGeneral: "",
    prjCover: {
      echoUrl: "",
      commUrl: "",
    },
    prjTagList: [],
    prjTargetList: [],
    prjAreaList: [],
  };
  formatData.disable = data.disable;
  formatData.prjType = typeDict_noDefault[data.prjType] as
    | "1"
    | "2"
    | "3"
    | "4";
  console.log("formatting__prjType: " + formatData.prjType);
  formatData.prjName = data.title;
  formatData.prjAim = data.purpose;
  formatData.prjGeneral = data.description;
  formatData.prjCover = {
    echoUrl: data.longUrl,
    commUrl: data.shortUrl,
  };
  if (data.prjTags) {
    formatData.prjTagList = data.prjTags.map(
      (t: { id: number; content: string }) => {
        const ret: tagType = {
          id: t.id.toString(),
          name: t.content,
          isSelected: true,
        };
        return ret;
      }
    );
  }
  if (data.targetKlgs) {
    switch (formatData.prjType) {
      case "4":
        formatData.prjAreaList = data.targetKlgs.map(
          (t: { areaCode: string; title: string }) => {
            const ret: tagType = {
              id: t.areaCode,
              name: t.title,
              isSelected: true,
            };
            return ret;
          }
        );
        break;
      default:
        formatData.prjTargetList = data.targetKlgs.map(
          (t: { klgCode: string; title: string }) => {
            const ret: tagType = {
              id: t.klgCode,
              name: t.title,
              isSelected: true,
            };
            return ret;
          }
        );
        break;
    }
  }
  console.log(
    "-->format data in step1 done: format type, coverUrl, tags, targets"
  );
  return formatData;
};
export const checkCoverUrl = (rule: any, value: any, callback: any) => {
  if (!value || !value.commUrl) {
    return callback(new Error("请上传封面"));
  }
  callback();
};
export const checkVideoUrl = (rule: any, value: any, callback: any) => {
  if (!value || !value.commUrl) {
    return callback(new Error("请上传视频"));
  }
  callback();
};
export const docCookies = {
  getItem: function (sKey: string): string | null {
    return (
      decodeURIComponent(
        document.cookie.replace(
          new RegExp(
            "(?:(?:^|.*;)\\s*" +
              encodeURIComponent(sKey).replace(/[-.+*]/g, "\\{{input}}") +
              "\\s*\\=\\s*([^;]*).*$)|^.*$"
          ),
          "$1"
        )
      ) || null
    );
  },
  setItem: function (
    sKey: string,
    sValue: string,
    sDomain: string,
    sPath?: string,
    vEnd?: number | string | Date,
    bSecure?: boolean
  ): boolean {
    if (!sKey || /^(?:expires|max-age|path|domain|secure)$/i.test(sKey)) {
      return false;
    }
    let sExpires = "";
    if (vEnd) {
      switch (typeof vEnd) {
        case "number":
          sExpires =
            vEnd === Infinity
              ? "; expires=Fri, 31 Dec 9999 23:59:59 GMT"
              : "; max-age=" + vEnd;
          break;
        case "string":
          sExpires = "; expires=" + vEnd;
          break;
        case "object":
          sExpires = "; expires=" + (vEnd as Date).toUTCString();
          break;
      }
    }
    document.cookie =
      encodeURIComponent(sKey) +
      "=" +
      encodeURIComponent(sValue) +
      sExpires +
      (sDomain ? "; domain=" + sDomain : "") +
      (sPath ? "; path=" + sPath : "") +
      (bSecure ? "; secure" : "");
    return true;
  },
  removeItem: function (sKey: string, sPath: string, sDomain: string): boolean {
    if (!sKey || !this.hasItem(sKey)) {
      return false;
    }
    document.cookie =
      encodeURIComponent(sKey) +
      "=; expires=Thu, 01 Jan 1970 00:00:00 GMT" +
      (sDomain ? "; domain=" + sDomain : "") +
      (sPath ? "; path=" + sPath : "");
    return true;
  },
  hasItem: function (sKey: string): boolean {
    return new RegExp(
      "(?:^|;\\s*)" +
        encodeURIComponent(sKey).replace(/[-.+*]/g, "\\{{input}}") +
        "\\s*\\="
    ).test(document.cookie);
  },
  keys: /* optional method: you can safely remove it! */ function (): string[] {
    const aKeys = document.cookie
      .replace(/((?:^|\s*;)[^=]+)(?=;|$)|^\s*|\s*(?:=[^;]*)?(?:1|$)/g, "")
      .split(/\s*(?:=[^;]*)?;\s*/);
    for (let nIdx = 0; nIdx < aKeys.length; nIdx++) {
      aKeys[nIdx] = decodeURIComponent(aKeys[nIdx]);
    }
    return aKeys;
  },
}
