import { defineStore } from "pinia"
import { getUserInfoApi } from "@/apis/path/userinfo"
import { ref } from "vue"

export const userInfoStore = defineStore("userInfo", () => {
  const userId = ref('')
  const username = ref('')
  const avatar = ref('')
  const permission = ref([])
  // 设置用户权限

  // 从后端获取权限
  const getUserInfo = (): Promise<Object> => {
    return new Promise((resolve, reject) => {
      getUserInfoApi()
        .then((res) => {
          if (res.success) {
            const data = res.data
            userId.value = data.userId
            username.value = data.userName
            avatar.value = data.avatar
            permission.value = data.permission
            resolve(data)
          } else {
            reject(new Error("获取用户信息失败"))
          }
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  const getUserId = () => userId.value
  const getAvatar = () => avatar.value
  const getPermission = () => permission.value
  const getUsername = () => username.value
  return {
    getUserInfo, getUserId, getAvatar, getPermission, getUsername
  }
})
 

