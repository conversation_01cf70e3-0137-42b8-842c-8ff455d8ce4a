<script setup lang="ts">
import { inject, nextTick, onBeforeUnmount, onMounted, ref, watch } from "vue"
import MyUploadAdapter from "@/utils/ckEditorImgUploader"
import { defineModel } from "vue"
import { listenSave, removeListenSave, autoSave } from "@/utils/ckListenerFunc"

const Editor = inject("Editor") as any

const props = defineProps({
  focusSave: {
    type: Boolean,
    default: false,
  },
  saveCallBack: {
    type: Function,
    default: () => {
      console.log("save-call-back Function LOCK")
    },
  },
})

const editorConfig = {
  placeholder: "请输入",
  extraPlugins: [MyCustomUploadAdapterPlugin],
}
const model = defineModel()
const focusing = ref(false)
let timer: number | null | undefined
let timeoutToStop: number | undefined

function MyCustomUploadAdapterPlugin(editor) {
  editor.plugins.get("FileRepository").createUploadAdapter = (loader) => {
    return new MyUploadAdapter(loader)
  }
}

const handleOnFocus = () => {
  console.log("onfocus")
  if (!props.focusSave) return
  focusing.value = true
  listenSave(document, props.saveCallBack)

}
const handleOnBlur = () => {
  console.log("onblur")
  if (!props.focusSave) return
  focusing.value = false
  stopTimer()
  removeListenSave(document)
}
// 开始轮询保存
const startTimer = () => {
  timer = setInterval(() => {
    console.log("该保存了！")
    autoSave(props.saveCallBack, 100)
  }, 5000)
}
// 停止轮询保存
const stopTimer = () => {
  if (timer) {
    clearInterval(timer as number)
    clearTimeout(timeoutToStop)
    timer = null
  }
}
// 延时轮询保存
const timeoutToStopTimer = () => {
  if (timer) {
    clearTimeout(timeoutToStop)
  }
  timeoutToStop = setTimeout(() => {
    console.log("超时关闭定时器")
    stopTimer()
  }, 10000)
}
watch(
  () => model.value,
  (oldValue, newValue) => {
    if (oldValue !== newValue) {
      nextTick(() => {
        if (timer) {
          timeoutToStopTimer()
        }
      })
      if (!timer && focusing.value) startTimer()
    }
  }
)
onBeforeUnmount(() => {
  stopTimer()
})
</script>

<template>
  <div class="ckeditor-wrapper">
    <ckeditor
      :editor="Editor.InlineEditorCustom"
      v-model="model"
      :config="editorConfig"
      @focus="handleOnFocus"
      @blur="handleOnBlur"
      class="input"
    >
    </ckeditor>
  </div>
</template>

<style scoped>
.ckeditor-wrapper {
  width: 100%;
  border: 1px solid var(--color-light);
  border-radius: 5px;
  .ck.ck-editor__editable_inline > :last-child {
    margin-bottom: 5px;
  }

  .ck.ck-editor__editable_inline > :first-child {
    margin-top: 5px;
  }

  .inlineEditor {
    /* 不知道为什么inline模式的边框不见了，手动设置了成了classic的边框颜色 */
    border: 1px solid #ccced1;
  }
}
</style>
