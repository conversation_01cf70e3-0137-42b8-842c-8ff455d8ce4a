!function(e,r){if("object"==typeof exports&&"object"==typeof module)module.exports=r(require("katex"));else if("function"==typeof define&&define.amd)define(["katex"],r);else{var t="object"==typeof exports?r(require("katex")):r(e.katex);for(var a in t)("object"==typeof exports?exports:e)[a]=t[a]}}("undefined"!=typeof self?self:this,(function(e){return function(){"use strict";var r={771:function(r){r.exports=e}},t={};function a(e){var o=t[e];if(void 0!==o)return o.exports;var n=t[e]={exports:{}};return r[e](n,n.exports,a),n.exports}a.n=function(e){var r=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(r,{a:r}),r},a.d=function(e,r){for(var t in r)a.o(r,t)&&!a.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:r[t]})},a.o=function(e,r){return Object.prototype.hasOwnProperty.call(e,r)};var o={};return function(){var e=a(771),r=a.n(e);const t={"(":"left parenthesis",")":"right parenthesis","[":"open bracket","]":"close bracket","\\{":"left brace","\\}":"right brace","\\lvert":"open vertical bar","\\rvert":"close vertical bar","|":"vertical bar","\\uparrow":"up arrow","\\Uparrow":"up arrow","\\downarrow":"down arrow","\\Downarrow":"down arrow","\\updownarrow":"up down arrow","\\leftarrow":"left arrow","\\Leftarrow":"left arrow","\\rightarrow":"right arrow","\\Rightarrow":"right arrow","\\langle":"open angle","\\rangle":"close angle","\\lfloor":"open floor","\\rfloor":"close floor","\\int":"integral","\\intop":"integral","\\lim":"limit","\\ln":"natural log","\\log":"log","\\sin":"sine","\\cos":"cosine","\\tan":"tangent","\\cot":"cotangent","\\sum":"sum","/":"slash",",":"comma",".":"point","-":"negative","+":"plus","~":"tilde",":":"colon","?":"question mark","'":"apostrophe","\\%":"percent"," ":"space","\\ ":"space","\\$":"dollar sign","\\angle":"angle","\\degree":"degree","\\circ":"circle","\\vec":"vector","\\triangle":"triangle","\\pi":"pi","\\prime":"prime","\\infty":"infinity","\\alpha":"alpha","\\beta":"beta","\\gamma":"gamma","\\omega":"omega","\\theta":"theta","\\sigma":"sigma","\\lambda":"lambda","\\tau":"tau","\\Delta":"delta","\\delta":"delta","\\mu":"mu","\\rho":"rho","\\nabla":"del","\\ell":"ell","\\ldots":"dots","\\hat":"hat","\\acute":"acute"},n={prime:"prime",degree:"degrees",circle:"degrees",2:"squared",3:"cubed"},s={"|":"open vertical bar",".":""},l={"|":"close vertical bar",".":""},i={"+":"plus","-":"minus","\\pm":"plus minus","\\cdot":"dot","*":"times","/":"divided by","\\times":"times","\\div":"divided by","\\circ":"circle","\\bullet":"bullet"},c={"=":"equals","\\approx":"approximately equals","\u2260":"does not equal","\\geq":"is greater than or equal to","\\ge":"is greater than or equal to","\\leq":"is less than or equal to","\\le":"is less than or equal to",">":"is greater than","<":"is less than","\\leftarrow":"left arrow","\\Leftarrow":"left arrow","\\rightarrow":"right arrow","\\Rightarrow":"right arrow",":":"colon"},u={"\\underleftarrow":"left arrow","\\underrightarrow":"right arrow","\\underleftrightarrow":"left-right arrow","\\undergroup":"group","\\underlinesegment":"line segment","\\utilde":"tilde"},p=(e,r,a)=>{if(!e)return;let o;o="open"===r?e in s?s[e]:t[e]||e:"close"===r?e in l?l[e]:t[e]||e:"bin"===r?i[e]||e:"rel"===r?c[e]||e:t[e]||e,/^\d+$/.test(o)&&a.length>0&&/^\d+$/.test(a[a.length-1])?a[a.length-1]+=o:o&&a.push(o)},d=(e,r)=>{const t=[];e.push(t),r(t)},h=function(e,r,t){if(void 0===r&&(r=[]),e instanceof Array)for(let a=0;a<e.length;a++)h(e[a],r,t);else((e,r,t)=>{switch(e.type){case"accent":d(r,(r=>{h(e.base,r,t),r.push("with"),p(e.label,"normal",r),r.push("on top")}));break;case"accentUnder":d(r,(r=>{h(e.base,r,t),r.push("with"),p(u[e.label],"normal",r),r.push("underneath")}));break;case"accent-token":case"color-token":case"kern":case"leftright-right":case"size":case"infix":case"internal":break;case"atom":{const{text:t}=e;switch(e.family){case"bin":p(t,"bin",r);break;case"close":p(t,"close",r);break;case"inner":p(e.text,"inner",r);break;case"open":p(t,"open",r);break;case"punct":p(t,"punct",r);break;case"rel":p(t,"rel",r);break;default:throw e.family,new Error('"'+e.family+'" is not a valid atom type')}break}case"color":{const a=e.color.replace(/katex-/,"");d(r,(r=>{r.push("start color "+a),h(e.body,r,t),r.push("end color "+a)}));break}case"delimsizing":e.delim&&"."!==e.delim&&p(e.delim,"normal",r);break;case"genfrac":d(r,(r=>{const{leftDelim:a,rightDelim:o}=e;e.hasBarLine?(r.push("start fraction"),a&&p(a,"open",r),h(e.numer,r,t),r.push("divided by"),h(e.denom,r,t),o&&p(o,"close",r),r.push("end fraction")):(r.push("start binomial"),a&&p(a,"open",r),h(e.numer,r,t),r.push("over"),h(e.denom,r,t),o&&p(o,"close",r),r.push("end binomial"))}));break;case"hbox":case"lap":case"ordgroup":case"raisebox":case"sizing":case"styling":case"smash":case"vcenter":case"operatorname":case"font":case"html":h(e.body,r,t);break;case"leftright":d(r,(r=>{p(e.left,"open",r),h(e.body,r,t),p(e.right,"close",r)}));break;case"mathord":p(e.text,"normal",r);break;case"op":{const{body:a,name:o}=e;a?h(a,r,t):o&&p(o,"normal",r);break}case"op-token":case"textord":p(e.text,t,r);break;case"overline":d(r,(function(r){r.push("start overline"),h(e.body,r,t),r.push("end overline")}));break;case"pmb":r.push("bold");break;case"phantom":r.push("empty space");break;case"rule":r.push("rectangle");break;case"spacing":r.push("space");break;case"sqrt":d(r,(r=>{const{body:a,index:o}=e;if(o)return"3"===b(h(o,[],t)).join(",")?(r.push("cube root of"),h(a,r,t),void r.push("end cube root")):(r.push("root"),r.push("start index"),h(o,r,t),void r.push("end index"));r.push("square root of"),h(a,r,t),r.push("end square root")}));break;case"supsub":{const{base:a,sub:o,sup:s}=e;let l=!1;if(a&&(h(a,r,t),l="op"===a.type&&"\\log"===a.name),o){const e=l?"base":"subscript";d(r,(function(r){r.push("start "+e),h(o,r,t),r.push("end "+e)}))}s&&d(r,(function(e){const r=b(h(s,[],t)).join(",");r in n?e.push(n[r]):(e.push("start superscript"),h(s,e,t),e.push("end superscript"))}));break}case"text":if("\\textbf"===e.font){d(r,(function(r){r.push("start bold text"),h(e.body,r,t),r.push("end bold text")}));break}d(r,(function(r){r.push("start text"),h(e.body,r,t),r.push("end text")}));break;case"enclose":if(/cancel/.test(e.label)){d(r,(function(r){r.push("start cancel"),h(e.body,r,t),r.push("end cancel")}));break}if(/box/.test(e.label)){d(r,(function(r){r.push("start box"),h(e.body,r,t),r.push("end box")}));break}if(/sout/.test(e.label)){d(r,(function(r){r.push("start strikeout"),h(e.body,r,t),r.push("end strikeout")}));break}if(/phase/.test(e.label)){d(r,(function(r){r.push("start phase angle"),h(e.body,r,t),r.push("end phase angle")}));break}throw new Error("KaTeX-a11y: enclose node with "+e.label+" not supported yet");case"vphantom":throw new Error("KaTeX-a11y: vphantom not implemented yet");case"hphantom":throw new Error("KaTeX-a11y: hphantom not implemented yet");case"array":throw new Error("KaTeX-a11y: array not implemented yet");case"raw":throw new Error("KaTeX-a11y: raw not implemented yet");case"url":throw new Error("KaTeX-a11y: url not implemented yet");case"tag":throw new Error("KaTeX-a11y: tag not implemented yet");case"verb":p("start verbatim","normal",r),p(e.body,"normal",r),p("end verbatim","normal",r);break;case"environment":throw new Error("KaTeX-a11y: environment not implemented yet");case"horizBrace":p("start "+e.label.slice(1),"normal",r),h(e.base,r,t),p("end "+e.label.slice(1),"normal",r);break;case"includegraphics":throw new Error("KaTeX-a11y: includegraphics not implemented yet");case"href":throw new Error("KaTeX-a11y: href not implemented yet");case"cr":throw new Error("KaTeX-a11y: cr not implemented yet");case"underline":d(r,(function(r){r.push("start underline"),h(e.body,r,t),r.push("end underline")}));break;case"xArrow":throw new Error("KaTeX-a11y: xArrow not implemented yet");case"cdlabel":throw new Error("KaTeX-a11y: cdlabel not implemented yet");case"cdlabelparent":throw new Error("KaTeX-a11y: cdlabelparent not implemented yet");case"mclass":{const t=e.mclass.slice(1);h(e.body,r,t);break}case"mathchoice":h(e.text,r,t);break;case"htmlmathml":h(e.mathml,r,t);break;case"middle":p(e.delim,t,r);break;default:throw e.type,new Error("KaTeX a11y un-recognized type: "+e.type)}})(e,r,t);return r},b=function(e){let r=[];return e.forEach((function(e){e instanceof Array?r=r.concat(b(e)):r.push(e)})),r};o.default=function(e,t){const a=r().__parse(e,t),o=h(a,[],"normal");return b(o).join(", ")}}(),o=o.default}()}));