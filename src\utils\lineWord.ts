import {getWord} from "@/apis/path/lineWordApis";
import type {questionType} from "@/utils/type";

export default function useLineWord() {
  /**
   * 划词功能函数 直接调用此函数 返回可与后端交互的字符串
   * @returns 划词结果 可与后端交互的字符串
   */
  const handleLineWord = () => {
    // 划词的内容
    const selectedText = window.getSelection();
    const selectRange = selectedText!.getRangeAt(0);
    // console.log(selectRange);
    // console.log(selectRange.cloneContents());
    // console.log(selectedText);
    // 限制行为 当有内容时 再触发函数
    if (selectRange.collapsed) {
      return '';
    }
    const sc = selectRange.startContainer;
    const ec = selectRange.endContainer;
    const scParentNode = sc.parentNode;
    const ecParentNode = ec.parentNode;
    // 先做单字处理
    const selectedTextStr = selectedText!.toString();
    if (selectedTextStr.length == 1 && selectedTextStr.trim().length != 0) {
      // 字符长度为1  是单个字
      // 判断当前单个字 是公式 还是正常文字
      if (!scParentNode!.isMathJax) {
        // 不是公式内的单个字
        return scParentNode!.outerHTML;
      }
    }
    // 部分公式 仅仅包含部分公式
    if (scParentNode!.isMathJax && ecParentNode!.isMathJax) {
      const parentId = scParentNode!.id;
      return getOriginJax(parentId);
    }

    // 只有一个图片的情况处理

    // 正则匹配
    // const spanReg = /<span data-index="[^"]*" data-qid="[^"]*" onclick="[^"]*">(.*?)<\/span>/g;
    const spanReg = /<span data-index="[^"]*" data-qid="[^"]*" onclick="[^"]*"(?: class="[^"]*")?>(.*?)<\/span>/g;
    const contentStr = documentFragmentToString(selectRange.cloneContents());
    // 拦截一下图片的情况
    if (contentStr.indexOf('<img') == 0) {
      return sc.outerHTML;
    }
    const spanResult = contentStr.match(spanReg);
    console.log('s', spanResult);

    const jaxReg = /MathJax-Element-(\d*)-Frame/g;
    const jaxResult = contentStr.match(jaxReg);
    const jaxStrArr: any = []; // 存储所有的公式结果
    if (jaxResult != null) {
      // 对公式内容进行拼接 不采用替换的方法
      jaxResult.forEach((id) => {
        const re = getOriginJax(id);
        jaxStrArr.push(re);
      });
    }
    if (spanResult == null) {
      return;
    }
    // 开头结尾处理部分
    const nullReg = /<span data-index="[^"]*" data-qid="[^"]*" onclick="[^"]*"><\/span>/g;
    // 开头判断
    if (nullReg.test(spanResult![0])) {
      spanResult!.shift();
    }
    if (nullReg.test(spanResult![spanResult!.length - 1])) {
      spanResult!.pop();
    }
    // 最终字符串结果处理部分
    let currentJax = 0;
    let finalStr = '';
    let empty = 1; // 空格处理 如果都是空格 值为1  如果有非空格 值为0
    const emptyReg =
      /<span data-index="[^"]*" data-qid="[^"]*" onclick="[^"]*">((&nbsp;)| )<\/span>/g;
    for (let i = 0; i < spanResult!.length; i++) {
      // 先判断空格的情况
      if (!emptyReg.test(spanResult![i])) {
        empty = 0;
      }
      if (spanResult![i].indexOf('Math') != -1) {
        spanResult![i] = jaxStrArr[currentJax];
        currentJax++;
      }
      // 处理有a标签的情况 遇到a标签就跳过
      if (spanResult![i].indexOf('</a>') != -1) {
        continue;
      }
      finalStr += spanResult![i];
    }

    if (empty) {
      return '';
    }

    return finalStr;
  };
  /**
   * 点击高亮词汇 把和后端交互的过程封装起来了
   * @returns 通过qid要到的问题列表 examSectionType[]
   */
  const getQuestionList = async (id: string) => {
    const res = await getWord(id);
    if (res.success) {
      const questionList: questionType[] = res.data.questionAndAnswer.map(qa => {
        return {
          qId: qa.question.oid,
          isValid: true,
          relatedText: qa.question.associatedWords,
          qContent: qa.question.keyWords,
          qType: qa.question.questionType, // 是什么 | 为什么
          qMode: qa.question.questionNecessity, // 必要 | 参考
          klg: qa.answers[0].answerKlgs.map(k => {
            return {
              id: k.klgCode,
              name: k.title,
            }
          }),
          explanation: qa.answers[0].answerExplanation,
        }
      })
      console.log(questionList);
      return questionList;
    }
    else {
      console.log('获取问题列表失败');
    }
  }

  // 将documentfragment 转成字符串
  const documentFragmentToString = (df: any) => {
    // 克隆 DocumentFragment 中的内容到一个 div 元素中
    var div = document.createElement('div');
    div.appendChild(df.cloneNode(true));
    // 将 div 的内容转换为字符串
    return div.innerHTML;
  };
  // 获取原始公式的函数
  const getOriginJax = (parentId: any) => {
    // 获取到父元素
    let elParent = document.getElementById(parentId);
    while (elParent!.id.indexOf('Frame') == -1) {
      elParent = elParent!.parentNode;
    }
    // 不等于-1 则已经到了最外层
    const index = elParent!.id.split('-')[2];
    const elFrom = document.getElementById(`MathJax-Element-${index}`);
    const elFormParent = elFrom!.parentElement;
    const result = `<span data-index="${elFormParent!.getAttribute(
      'data-index'
    )}" data-qid="${elFormParent!.getAttribute(
      'data-qid'
    )}" onclick="handleWord(this)"><script type="${elFrom!.type}">${
      elFrom!.innerHTML
    }<\/script><\/span>`;
    return result;
  };

  /**
   * 根据划词接口返回的结果更新span元素上的qid
   * @param val 传入划词接口返回的结果数组 默认所有划词提交接口返回的数据结构相同
   */
  const updataDom = (val: any) => {
    val.forEach((item: any) => {
      const element = document.querySelector(`[data-index="${item.dataIndex}"]`);
      // 检查是否找到元素
      if (element) {
        element.setAttribute('data-qid', item.dataQid);
      } else {
        console.log('未找到元素');
      }
    });
  };
  /**
   * 为有问题的span添加划词标记
   * 在切换为阅读模式时触发此函数
   */
  const markWord = () => {
    // 获取所有span
    const spanArr = document.querySelectorAll('[data-qid]');
    // console.log('spanArr',spanArr)
    spanArr.forEach((item: Element) => {
      if (item.getAttribute('data-qid') != '') {
        // @ts-ignore
        item.className = 'highLight';
        // item.style.color = 'blue';
      }
    });
  };
  /**
   * 取消span上的划词标记
   * 在切换为提问/划词模式时触发该函数
   */
  const unmarkWord = () => {
    // 获取所有span
    const spanArr = document.querySelectorAll('[data-qid]');
    spanArr.forEach((item) => {
      if (item.getAttribute('data-qid') != '') {
        item.removeAttribute('class');
      }
    }
    )
    ;
  };

  return {
    handleLineWord,
    updataDom,
    markWord,
    unmarkWord,
    getQuestionList
  };
}
