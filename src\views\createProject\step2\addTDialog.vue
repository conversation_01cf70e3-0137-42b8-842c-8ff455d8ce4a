<script setup lang="ts">
import {
  onMounted,
  reactive,
  ref,
  watch,
  nextTick,
  onUpdated,
  watchEffect,
  onBeforeMount,
} from "vue"
import MyButton from "@/views/common/myButton.vue"
import MyTag from "@/views/common/myTag.vue"
import { editingPrjStore } from "@/stores/editingPrj"
import type { tagType } from "@/utils/type"
import {
  getAreaList,
  getTagList,
  getTargetList,
  type params2tList,
} from "@/apis/path/createProject"
import { ElMessage } from "element-plus"
import { Search } from "@element-plus/icons-vue"


const emit = defineEmits(["submit"])
const isDialogShow = ref(false)
const tForm = ref("") // tag | target | area
const dialogTitle = ref("")
const searchKey = ref("")
const current = ref(1)
const editingPrj = editingPrjStore()
const tList = ref<tagType[]>([])
const selectedTList = ref<tagType[]>([])
const limit = ref(-1) // 在step3的时候，限制选择条数

const total = ref(0)

const maxWidth = 9
// 检测是否省略 TODO: 修改逻辑，最好是判断渲染后的宽度
const isEllipsis = (data: string) => {
  // console.log("[data]", data);
  // console.log("[len]", data.length);
  const cleanedData = data.replace(/<[^>]*>/g, "")
  // console.log("cleanedData", cleanedData ," = ",cleanedData.length);
  return cleanedData.length > maxWidth ? true : false
}
const showDialog = (
  form: string,
  step: number,
  maxLimit?: number,
  sTList?: tagType[]
) => {
  if (maxLimit != undefined) {
    limit.value = maxLimit
  }
  isDialogShow.value = true
  tForm.value = form
  dialogTitle.value =
    form === "tag" ? "添加标签" : form === "klg" ? "添加目标" : "添加领域"
  // 数据初始化
  current.value = 1
  tList.value = []
  if (step == 1) {
    if (tForm.value == "tag") {
      selectedTList.value = [...editingPrj.getPrjTagList()]
    } else if (tForm.value == "klg") {
      selectedTList.value = [...editingPrj.getPrjTargetList()]
    } else if (tForm.value == "area") {
      selectedTList.value = [...editingPrj.getPrjAreaList()]
    }
  }
  if (sTList) {
    selectedTList.value = [...sTList]
  }
  if (limit.value != -1) {
    // 限制选择条数
    selectedTList.value = selectedTList.value.slice(0, limit.value)
  }
  // FIXME:目前标签开启了自动搜索，因为key为空时可以搜出来全部标签
  // 目标知识点搜索，key为空时搜不到东西，开了就是浪费通信资源
  if (tForm.value == "tag") getTList()
}
const handleWheelFn = (e) => {
  // 如果已经是全部的标签，直接返回
  if (tList.value && tList.value.length >= total.value) return
  let bottom = document.querySelector("#bottom")?.offsetTop as number
  let now = document.querySelector("#scroll")?.scrollTop as number
  setTimeout(() => {
    if (now >= bottom && e.deltaY > 0) {
      // TODO: 暂未启用
      console.log("bottom! ")
    }
  }, 100)
}
const getTList = () => {
  // 和后端通信+洗数据
  // 1.保证tag和target数据结构一致（都弄成tagType类型）
  // 2.保证choose字段和值和前端暂存数据selectedTList一致
  // FIXME: 后端还没做分页
  let param = ref<params2tList>({
    current: current.value,
    limit: 5,
    keywords: searchKey.value,
    status: 1,
  })
  if (tForm.value === "tag") {
    getTagList(param.value)
      .then((res) => {
        if (res.success) {
          // 翻页：追加写
          // 如果后端改结构了，这里t的结构也要改
          let appendList: tagType[] = res.data.list?.map(
            (t: { id: number; content: string; choose: boolean }) => {
              const matchedTag = selectedTList.value.find(
                (st: tagType) => st.id === t.id.toString()
              )
              if (matchedTag) {
                return {
                  id: t.id.toString(),
                  name: t.content,
                  isSelected: true,
                }
              } else {
                return {
                  id: t.id.toString(),
                  name: t.content,
                  isSelected: t.choose,
                }
              }
            }
          )
          tList.value.push(...appendList)
          // console.log(JSON.stringify(tList.value, null ,2))
        } else {
          ElMessage.error("加载标签列表异常")
        }
      })
      .catch()
  } else if (tForm.value === "klg") {
    getTargetList(param.value)
      .then((res) => {
        if (res.success) {
          // 翻页：追加写
          // 如果后端改结构了，这里t的结构也要改
          let appendList: tagType[] = res.data.list?.map(
            (t: { klgCode: string; klgTitle: string; choose: boolean }) => {
              const matchedTarget = selectedTList.value.find(
                (st: tagType) => st.id === t.klgCode
              )
              if (matchedTarget) {
                return {
                  id: t.klgCode,
                  name: t.klgTitle,
                  // name: "<p>\<script type=\"math/tex\"\>x\<\/script\></p>",
                  isSelected: matchedTarget.isSelected,
                }
              } else {
                return {
                  id: t.klgCode,
                  name: t.klgTitle,
                  // name: "<p>\<script type=\"math/tex\"\>x\<\/script\></p>",
                  isSelected: t.choose,
                }
              }
            }
          )
          tList.value.push(...appendList)
          // console.log('==>' + JSON.stringify(tList.value, null ,2))
        } else {
          ElMessage.error("加载目标列表异常")
        }
      })
      .catch()
  } else if (tForm.value === "area") {
    getAreaList(param.value)
      .then((res) => {
        if (res.success) {
          // 翻页：追加写
          // 如果后端改结构了，这里t的结构也要改
          let appendList: tagType[] = res.data.map(
            (t: { areaCode: string; title: string }) => {
              const matchedTarget = selectedTList.value.find(
                (st: tagType) => st.id === t.areaCode
              )
              if (matchedTarget) {
                return {
                  id: t.areaCode,
                  name: t.title,
                  // name: "<p>\<script type=\"math/tex\"\>x\<\/script\></p>",
                  isSelected: matchedTarget.isSelected,
                }
              } else {
                return {
                  id: t.areaCode,
                  name: t.title,
                  // name: "<p>\<script type=\"math/tex\"\>x\<\/script\></p>",
                  isSelected: false,
                }
              }
            }
          )
          tList.value.push(...appendList)
          // console.log('==>' + JSON.stringify(tList.value, null ,2))
        } else {
          ElMessage.error("加载领域列表异常")
        }
      })
      .catch()
  }
  current.value++
}

const handleSearch = async () => {
  // 数据初始化

  current.value = 1
  tList.value = []
  await getTList()

  // watch 变量不可以 watchEffect可以，不知道为什么
  // watchEffect(() => {
  //   const htmlContent1Elements = document.getElementsByClassName("htmlContent1")
  //   console.log("htmlContent1",htmlContent1Elements.length)
  //   console.log("tList",tList.value.length)
  //   if (htmlContent1Elements.length == tList.value.length) {
  //   }
  // })

  //TODO 先持续监听判断dom子元素的渲染完成数目是否等于getlist中
  //TODO 返回的列表元素数目，只有当这两个元素相等的时候，才调用
  //TODO mathqueue进行公式扫描渲染
  // setTimeout(() =>

  // nextTick(() =>
  //   );
}

const handleSelect = (item: tagType) => {
  if (limit.value != -1 && selectedTList.value.length >= limit.value) {
    ElMessage.error(
      "最多选择" +
        limit.value +
        "个" +
        (tForm.value === "area" ? "领域" : "标签")
    )
    return
  }
  item.isSelected = !item.isSelected
  const foundItem = tList.value.find((i) => i.id === item.id)
  if (foundItem) {
    // 维护selectedTList
    const index = selectedTList.value.findIndex(
      (i: tagType) => i.id === item.id
    )
    if (index !== -1) {
      selectedTList.value.splice(index, 1)
    } else {
      selectedTList.value.push(item)
    }
  }
}
const handleDelete = (aimId: string) => {
  // 只维护selectedTList
  const aimItem = tList.value.find((t) => t.id == aimId)
  if (aimItem) {
    aimItem.isSelected = false
  }
  const index = selectedTList.value.findIndex((i: tagType) => i.id === aimId)
  if (index !== -1) {
    selectedTList.value.splice(index, 1)
  }
}
const handleClose = () => {
  isDialogShow.value = false
}
const handleSubmit = () => {
  emit("submit", selectedTList.value, tForm.value)
  isDialogShow.value = false
}
defineExpose({
  showDialog,
})
onMounted(() => {
  getTList()
})
</script>

<template>
  <el-dialog :close-on-click-modal="false" v-model="isDialogShow" width="596">
    <template #header="{ titleId }">
      <div :id="titleId" class="title">
        {{ dialogTitle }}
      </div>
    </template>
    <div class="content-container">
      <div class="searchBar">
        <el-input
          class="input"
          v-model="searchKey"
          @keydown.enter="handleSearch()"
          :placeholder="
            '请输入' +
            (tForm === 'tag' ? '标签' : tForm === 'klg' ? '目标' : '领域') +
            '名称'
          "
        >
          <template #suffix>
            <el-icon class="btn el-input__icon" @click="handleSearch()">
              <Search />
            </el-icon>
          </template>
        </el-input>
      </div>
      <div class="tList" @wheel="handleWheelFn" id="scroll">
        <div v-if="tList.length === 0" class="empty-list">
          <span>搜索结果为空</span>
        </div>
        <div
          class="tItem"
          :class="item.isSelected ? 'isSelected' : ''"
          v-for="item in tList"
          :key="item.id"
          @click="handleSelect(item)"
        >
          <div class="htmlContent1 ck-content" v-html="item.name"></div>
          <!--          {{item.name}}-->
        </div>
      </div>
      <!-- target列表 -->
      <div class="selectedTList" id="bottom">
        <my-tag
          class="t"
          @delete="handleDelete"
          :tag-id="item.id"
          v-for="item in selectedTList"
          :key="item.id"
        >
          <el-tooltip raw-content popper-class="tooltip-width">
            <template #content>
              <div class="ck-content" v-html="item.name"></div>
            </template>
            <span class="ck-content" v-html="item.name"></span>
          </el-tooltip>
        </my-tag>
      </div>
    </div>
    <template #footer>
      <div class="footer">
        <div class="btn-group">
          <my-button type="light" @click="handleClose">关闭</my-button>
          <my-button @click="handleSubmit">确认</my-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
/* el-dialog的渲染是通过teleport直接挂到body下边，在这里写样式不生效，所以把样式写到全局(App.vue)了 */
.title {
  font-size: 16px;
  font-family: var(--font-family-text);
}

.content-container {
  /*--el-color-primary: var(--color-primary);*/
  font-family: var(--font-family-text);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .searchBar {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 35px;
    margin-bottom: 30px;

    .input {
      --el-color-primary: var(--el-border-color);
      width: 536px;

      .btn {
        cursor: pointer;
      }
    }
  }

  .tList {
    position: relative;
    display: flex;
    flex-direction: column;
    height: 215px;
    overflow-y: auto;
    margin-bottom: 10px;
    width: 100%;

    .tItem {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      width: 100%;
      /*height: 35px;*/
      line-height: 35px;
      padding: 0 20px;
      border: 1px solid var(--color-boxborder);
      cursor: pointer;
      margin-bottom: 10px;

      &:hover,
      &.isSelected {
        background-color: var(--color-light);
      }
    }

    &::-webkit-scrollbar {
      position: absolute;
      /*滚动条整体样式*/
      width: 5px;
      /*高宽分别对应横竖滚动条的尺寸*/
      height: 1px;
    }

    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 5px;
      background: var(--color-grey);
    }
  }
  .selectedTList {
    width: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    overflow: auto;
    padding-top: 10px;

    .t {
      margin: 0 10px 10px 0;
    }

    /* &::after {
      content: "";
      height: 1px;
      width: 100%;
      background-color: var(--color-line);
      position: absolute;
      top: 0;
    } */
  }
}

.footer {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-top: 20px;

  .btn-group {
    display: flex;
    justify-content: space-between;
    width: 280px;
  }
}
.empty-list {
  display: flex;
  justify-content: center;
}
</style>
