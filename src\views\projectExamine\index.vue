<script setup lang="ts">
import FormSwitch from "@/views/common/formSwitch.vue";
import {
  prjForm,
  prjState,
  prjType,
  typeDict,
  stateDict,
  formDict,
} from "@/utils/constant";
import { nextTick, onMounted, ref, watchEffect } from "vue";
import MyButton from "@/views/common/myButton.vue";
import {
  type key2examineList,
  getMyExamineList,
  getWaitingExamineList,
  letMe2Examine,
  letOthers2Examine,
} from "@/apis/path/projectExamine";
import { ElMessage } from "element-plus";
import { useRouter } from "vue-router";
import { decodeData } from "@/utils/func";
import type { prjInfo4table } from "@/utils/type";
import MyFlipper from "@/views/common/myFlipper.vue";
import ExamDialog from "@/views/createProject/step2/examDialog.vue";
import ExamineDialog from "@/views/projectExamine/components/examineDialog.vue";
import { renderMarkdown } from "@/utils/markdown";

const router = useRouter();
const curPool = ref(1); // 1：我要审核的项目池 | 2：无人认领的待审核项目池
const prjTable = ref();
const curForm = ref();
const curType = ref(prjType.default.toString());
const tableData = ref();
const searchKey = ref("");
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const multipleSelection = ref<prjInfo4table[]>([]);
const examineDialogRef = ref();
const handleChangePool = (pool: number) => {
  getExmList(true).then(() => {
    curPool.value = pool;
  });
};
const handleSelect = (form: prjForm) => {
  // console.log(form);
  curForm.value = form == "3" ? "" : form.toString();
  getExmList();
};

const handleSearch = (key: string) => {
  console.log(key);
  searchKey.value = key;
  getExmList();
};
const getExmList = async (change?: boolean): Promise<boolean> => {
  const param: key2examineList = {
    current: currentPage.value,
    limit: pageSize.value,
    prForm: curForm.value,
    prType: curType.value,
    prAuthorName: searchKey.value, // FIXME：本功能后端暂时不支持(本版本不做支持）
    prName: searchKey.value,
  };

  let res: any = null;
  let getListFunc =
    curPool.value == 1 ? getMyExamineList : getWaitingExamineList;
  if (change) {
    getListFunc =
      curPool.value !== 1 ? getMyExamineList : getWaitingExamineList; // 这里要在拿到数据后变化pool的值所以是反的
  }
  try {
    res = await getListFunc(param);
    if (res.success) {
      tableData.value =
        res.data.list?.map((prj: prjInfo4table) => decodeData(prj)) || [];
      total.value = res.data.total;
      return true;
    } else {
      ElMessage.error(res.message);
      return false;
    }
  } catch (error) {
    console.error("Error fetching examine list:", error);
    return false;
  }
};
const changeExamine = (prj?: prjInfo4table) => {
  const prjList = prj ? [prj] : multipleSelection.value;
  if (prjList.length == 0) {
    ElMessage.error("请选择项目");
    return;
  }
  examineDialogRef.value.showDialog(curPool.value, prjList);
};

const handleExamine = (prjId: number, prjForm: string) => {
  const { href } = router.resolve({
    path: "/home/<USER>",
    query: {
      prjId: prjId,
      prjForm: formDict[prjForm],
    },
  });
  window.open(href, "_blank");
};
const handleChangePage = (newPage: number) => {
  currentPage.value = newPage;
  getExmList();
};
const handleSelectionChange = (val: prjInfo4table[]) => {
  console.log(val);
  multipleSelection.value = val;
};
const indexMethod = (index: number) => {
  return (currentPage.value - 1) * pageSize.value + index + 1;
};
const setCur = ($event: any) => {
  curType.value = $event.curType;
  getExmList();
};

onMounted(() => {
  getExmList();
  document.addEventListener("visibilitychange", () => {
    if (!document["hidden"]) {
      getExmList();
      // console.log('出现');
    } else {
      //隐藏
      // console.log('隐藏');
    }
  });
});
</script>

<template>
  <div class="main-container">
    <div class="switchPool-wrapper">
      <span class="switchPool-group">
        <span
          class="switchPool-button"
          :class="curPool == 1 ? 'selected' : ''"
          @click="handleChangePool(1)"
          >我的待审核项目</span
        >
        <span
          class="switchPool-button"
          :class="curPool == 2 ? 'selected' : ''"
          @click="handleChangePool(2)"
          >待审核项目池</span
        >
      </span>
    </div>
    <form-switch
      @search="handleSearch"
      @select="handleSelect"
      @cur="setCur($event)"
      placeholder="请输入项目标题或作者"
    >
    </form-switch>
    <div class="content-container">
      <div class="toolbar">
        <my-button type="primary" @click="changeExamine()">{{
          curPool == 1 ? "批量换人" : "我来审核"
        }}</my-button>
      </div>
      <div class="line"></div>
      <el-table
        ref="prjTable"
        :data="tableData"
        @selection-change="handleSelectionChange"
        style="width: 100%"
        empty-text="暂无数据"
      >
        <el-table-column
          type="selection"
          :width="curPool == 1 ? 43 : 48"
        ></el-table-column>
        <el-table-column
          type="index"
          :index="indexMethod"
          label="序号"
          :width="curPool == 1 ? 65 : 73"
        >
        </el-table-column>
        <el-table-column
          class="notFixWidth"
          prop="prname"
          label="项目名称"
          :width="curPool == 1 ? 225 : 253"
        >
          <template #default="scope">
            <span style="width: 100%; justify-content: flex-start">
              <span
                class="prname ck-content"
                v-html="renderMarkdown(scope.row.prname)"
              ></span>
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="prform"
          label="项目形式"
          :width="curPool == 1 ? 88 : 99"
        >
        </el-table-column>
        <el-table-column
          prop="prtype"
          label="项目类型"
          :width="curPool == 1 ? 124 : 140"
        ></el-table-column>
        <el-table-column
          prop="prAuthorName"
          label="作者"
          :width="curPool == 1 ? 126 : 142"
        ></el-table-column>
        <el-table-column
          prop="submitTime"
          label="提交日期"
          :width="curPool == 1 ? 206 : 232"
        ></el-table-column>
        <el-table-column
          prop="process"
          label="审核环节"
          :width="curPool == 1 ? 120 : 153"
        ></el-table-column>
        <!--    v-show不行，推测是组件底层的display优先级更高，所以display:none不生效    -->
        <el-table-column v-if="curPool == 1" label="操作" width="143">
          <template #default="scope">
            <span class="operationBlock">
              <span
                @click="handleExamine(scope.row.id, scope.row.prform)"
                class="operationBtn"
                >审核</span
              >
              <span @click="changeExamine(scope.row)" class="operationBtn"
                >换人审核</span
              >
            </span>
          </template>
        </el-table-column>
      </el-table>
      <my-flipper
        @change-page="handleChangePage"
        :current="currentPage"
        :page-size="pageSize"
        :total="total"
      ></my-flipper>
    </div>
  </div>
  <examine-dialog ref="examineDialogRef" @update="getExmList"></examine-dialog>
</template>

<style scoped>
.main-container {
  font-family: var(--font-family-text);
  color: var(--color-black);
  width: var(--width-content);
  /* margin: 20px auto auto auto; */

  .switchPool-wrapper {
    background-color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 70px;

    .switchPool-group {
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      width: 281px;
      height: 47px;
      background-color: var(--color-boxborder);

      .switchPool-button {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 139px;
        height: 45px;
        font-size: 14px;
        color: var(--color-black);
        cursor: pointer;

        &:hover {
          /*background-color: var(--color-primary);*/
          /*color: white;*/
        }

        &.selected {
          background-color: white;
          color: var(--color-primary);
        }
      }
    }
  }

  .content-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 5px 30px 30px 30px;
    background-color: white;

    .toolbar {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-direction: row;
      padding-bottom: 5px;

      .select {
        width: 180px;

        &:deep(.el-input) {
          --el-input-height: 35px;
          line-height: 35px;
        }
      }

      /* &::after {
        content: '';
        height: 1px;
        width: 100%;
        background-color: var(--color-line);
        position: absolute;
        bottom: 0;
      } */
    }

    .el-table {
      &:deep(.el-table__cell) {
        padding: 0;
      }

      &:deep(.cell) {
        justify-content: center;
        display: flex;
        align-items: center;
        min-height: 46px;
      }


    }

    .operationBlock {
      display: flex;
      width: 143px;
      justify-content: space-between;
      align-items: center;
      flex-direction: row;

      .operationBtn {
        color: var(--color-primary);
        &:hover {
          cursor: pointer;
          color: var(--color-primary);
          font-weight: 600;
          /* text-decoration: underline; */
        }
      }
    }
  }
}
</style>
