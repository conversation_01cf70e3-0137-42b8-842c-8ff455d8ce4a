import { uploadImage4CkEditor } from "@/apis/path/createProject"
import { http } from "@/apis"
class MyUploadAdapter {
  constructor(loader) {
    this.loader = loader
  }
  // 启动上载过程
  async upload() {
    // const data = new FormData()
    // data.append("upload", this.loader.file)

    // try {
    //   const res = await uploadImage4CkEditor(data)
    //   if (res.success) {
    //     return {
    //       default: res.data.url,
    //     }
    //   } else {
    //     throw new Error("Upload failed")
    //   }
    // } catch (error) {
    //   console.error("Upload error:", error)
    //   return {
    //     default: "",
    //   }
    // }

    const data = new FormData();
    data.append("upload",await this.loader.file);
    const res = await http({
        url: '/cos/file/imageCosUploadF', //给后端上传图片的接口
        method: 'post',
        data: data
    })
    return {
        default:res.data.url
    }
  }
  // 中止上载过程
  abort() {
    if (this.xhr) {
      this.xhr.abort()
    }
  }
}
export default MyUploadAdapter
