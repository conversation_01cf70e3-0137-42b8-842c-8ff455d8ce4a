<script setup lang="ts">

import { onBeforeMount, onBeforeUnmount, onMounted, provide, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import { editingPrjStore } from "@/stores/editingPrj";
import { getProjectDetail, submitProject } from "@/apis/path/createProject";
import { formatData_step1 } from "@/utils/func";
import type { prjInfoType, simpleSectionInfo, tagType } from "@/utils/type";
import { prjForm, prjType } from "@/utils/constant";
import PrjInfo from "@/views/common/prjInfo.vue";
import MySteps from "@/views/createProject/components/mySteps.vue";
import SectionSwitcher from "@/views/createProject/step3/sectionSwitcher.vue";
import MyButton from "@/views/common/myButton.vue";
import TextQuestion from "@/views/createProject/step3/textQuestion.vue";
import { ElMessage } from "element-plus";
import ExamWrapper from "@/views/createProject/step3/examWrapper.vue";
import VideoQuestion from "@/views/createProject/step3/videoQuestion.vue";

const route = useRoute();
const router = useRouter();
const editingPrj = editingPrjStore();
const curPrjForm = ref();
const curPrjId = ref();
const prjRlsDate = ref();
const curSecId = ref();
const curSecCount = ref(); // 只为单节视频提供
const wait2ShowSecId = ref(-1);
const saveDraftSectionId = ref();
const saveSectionId = ref();
// 把响应式数据provide出去,用于告知子组件保存
provide('saveDraftSection', saveDraftSectionId);
provide('saveSection', saveSectionId);
const prjInfoData = ref<prjInfoType>({
  prjType: prjType.exam,
  prjName: '',
  prjAim: '',
  prjGeneral: '',
  prjTagList: [] as tagType[],
  prjTargetList: [] as tagType[],
  prjAreaList: [] as tagType[],
  prjCover: {
    commUrl: '',
    echoUrl: '',
  }
});
const secList = ref<simpleSectionInfo[]>([]);
onBeforeMount(() => {
  curPrjId.value = parseInt(route.query.prjId as string);
  curPrjForm.value = editingPrj.getForm();
})
onMounted(() => {
  getProjectDetail(curPrjId.value).then((res: any) => {
    if (res.success) {
      let baseData = res.data.list[0];
      // 这个部分后端传来的和接受的都是typeName，前端存储都转换成了typeCode
      // 因为整个后端没有统一传输到底是code还是name，所以前端自己统一一下数据便于管理
      // 在formatData_step1中，把type字段转化成typeName
      prjInfoData.value = formatData_step1(baseData);
      // FIXME: 这个地方，至少文稿类，后端确实全量返回了sectionList
      secList.value = res.data.sectionList?.map((sec: any) => {
        return {
          sectionId: sec.sectionId,
          sectionName: sec.sectionTitle,
          sectionCount: sec.sectionNum,
        };
      });
      curSecId.value = secList.value[0].sectionId;
      curSecCount.value = secList.value[0].sectionNum;
      // editingSection.setSectionId(curSecId.value);
      // editingSection.setSectionType(curPrjForm.value, prjInfoData.value.prjType);
    }
    else {
      ElMessage.error('Step3项目信息获取失败');
    }
  })
})
const handleChangeSection = (newSectionId: number) => {
  // console.log(11111111111, newSectionId)
  curSecId.value = newSectionId;
}
const handlePreStep = () => {
  router.push({
    path: '/home/<USER>/step2',
    query: {
      prjId: curPrjId.value,
    }
  });
}
const handleNextStep = () => {
  submitProject(curPrjId.value).then(res => {
    if (res.success) {
      window.close();
    }
    else {
      ElMessage.error(res.message);
    }
  })
}
onBeforeUnmount(() => {
  // localStorage.setItem('refreshHomeTable', '1');
})
</script>

<template>
  <div class="main-container">
    <my-steps :action="3" :form="curPrjForm"></my-steps>
    <div class="content-wrapper">
      <prj-info :info-data="prjInfoData"></prj-info>
      <template
        v-if="(curPrjForm == prjForm.text && prjInfoData.prjType == prjType.exam) || (prjInfoData.prjType == prjType.prj)">
        <section-switcher @changeSection="handleChangeSection" :curSectionId="curSecId"
          :sectionList="secList"></section-switcher>
      </template>
      <template v-if="(curPrjForm == prjForm.text && prjInfoData.prjType == prjType.exam)">
        <!-- 文稿-> 测评 -->
        <exam-wrapper :cur-project-id="curPrjId" :cur-section-id="curSecId"></exam-wrapper>
      </template>
      <template v-else-if="curPrjForm == prjForm.text">
        <!-- 文稿-> 案例|讲解 -->
        <text-question :cur-project-id="curPrjId" :cur-section-id="curSecId"></text-question>
      </template>
      <template v-else-if="curPrjForm == prjForm.video">
        <!-- 视频 -->
        <video-question :cur-project-id="curPrjId" :cur-section-id="curSecId"></video-question>
      </template>
      <div class="tool-bar">
        <my-button type="light" @click="handlePreStep()">上一步</my-button>
        <my-button :clickable="false">存草稿</my-button>
        <my-button @click="handleNextStep()">提交</my-button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.main-container {
  font-family: var(--font-family-text);
  background-color: white;
  color: var(--color-black);
  width: var(--width-content);
  /* margin: 20px auto auto auto; */
  display: flex;
  flex-direction: column;
  align-items: center;

  .content-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    padding: 30px;
  }

  .tool-bar {
    width: 420px;
    display: flex;
    justify-content: space-between;
  }
}
</style>