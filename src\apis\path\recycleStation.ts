import { http } from "@/apis";
import type {APIResponse} from "@/utils/type";

export interface key2recycleList {
    current: number,
    limit: number,
    prForm?: string,
    prName?: string,
    prType?: string
}
export function getRecycleList(param: key2recycleList): Promise<APIResponse> {
    return http.request({
        method: 'post',
        url: '/recycle/bin/getMyRecBin',
        data: param,
    })
}
export function deleteRecycleList(idList: number[]): Promise<APIResponse> {
    return http.request({
        method: 'post',
        url: '/recycle/bin/delList',
        data: idList,
    })
}
export function recoverRecycleList(idList: number[]): Promise<APIResponse> {
    return http.request({
        method: 'post',
        url: '/recycle/bin/reBack',
        data: idList,
    })
}