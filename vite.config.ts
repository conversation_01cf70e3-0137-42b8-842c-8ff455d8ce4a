import { fileURLToPath, URL } from "node:url";
import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
import mkcert from "vite-plugin-mkcert";
import { codeInspectorPlugin } from 'code-inspector-plugin';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    codeInspectorPlugin({
      bundler: 'vite',
    }),
    AutoImport({
      resolvers: [ElementPlusResolver()],
    }),
    Components({
      resolvers: [ElementPlusResolver()],
    }),
    vue(),
    mkcert(),
  ],
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", import.meta.url)),
    },
  },
  server: {
    port: 5673,
    open: false,
    proxy: {
      "/wellerman-service": {
        target: "http://192.168.88.118:8001",
        changeOrigin: true, //开启代理
      },
      "/auth-service": {
        target: "http://192.168.88.118:8001",
        changeOrigin: true,
      },
    },
  },
  // optimizeDeps: {
  //   include: ['ckeditor5-custom-build', '@ckeditor/ckeditor5-vue']
  // },
  // build: {
  //   commonjsOptions: {
  //     include: [/ckeditor-super/, /ckeditor-vue/, /node_modules/]
  //   }
  // }
});
