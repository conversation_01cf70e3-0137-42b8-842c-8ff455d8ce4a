import {defineStore} from "pinia";
import {ref} from "vue";
import type {prjInfoType, tagType, imgUrl} from "@/utils/type";

export const editingPrjStore = defineStore('editingPrj', () => {
    const prjId = ref<number>();
    const prjForm = ref<number>();
    const prjDetail = ref<prjInfoType>({
        prjType: '',
        prjName: '',
        prjAim: '',
        prjGeneral: '',
        prjTagList: [],
        prjTargetList: [],
        prjAreaList: [],
        prjCover: {
            echoUrl: '',
            commUrl: '',
        },
    });
    const setPrjId = (id: number) => {
        if (prjId.value !== id) {
            prjId.value = id;
            prjDetail.value = {
                prjType: '',
                prjName: '',
                prjAim: '',
                prjGeneral: '',
                prjTagList: [],
                prjTargetList: [],
                prjAreaList: [],
                prjCover: {
                    echoUrl: '',
                    commUrl: '',
                },
            }
        }
    };
    const getPrjId = () => prjId.value;
    const setForm = (form: number) => prjForm.value = form;
    const getForm = () => prjForm.value;
    const setPrjCover = (newValue: imgUrl) => prjDetail.value.prjCover = newValue;
    const getPrjCover = () => prjDetail.value.prjCover;
    const setPrjTagList = (tagList: tagType[]) => prjDetail.value.prjTagList = tagList;
    const getPrjTagList = () => prjDetail.value.prjTagList;
    const removeTag = (aimId: string) => {
        const index = prjDetail.value.prjTagList.findIndex((i: tagType) => i.id === aimId);
        if (index !== -1) {
            prjDetail.value.prjTagList.splice(index, 1);
        }
    }
    const setPrjTargetList = (targetList: tagType[]) => prjDetail.value.prjTargetList = targetList;
    const getPrjTargetList = () => prjDetail.value.prjTargetList;
    const removeTarget = (aimId: string) => {
        const index = prjDetail.value.prjTargetList.findIndex((i: tagType) => i.id === aimId);
        if (index !== -1) {
            prjDetail.value.prjTargetList.splice(index, 1);
        }
    }
    const setPrjAreaList = (areaList: tagType[]) => prjDetail.value.prjAreaList = areaList;
    const getPrjAreaList = () => prjDetail.value.prjAreaList;
    const removeArea = (aimId: string) => {
        const index = prjDetail.value.prjAreaList.findIndex((i: tagType) => i.id === aimId);
        if (index !== -1) {
            prjDetail.value.prjAreaList.splice(index, 1);
        }
    }
    const getPrjDetail = () => prjDetail.value;
    const clearData = () => {
        prjId.value = 0;
        prjForm.value = 0;
        prjDetail.value = {
            prjType: '',
            prjName: '',
            prjAim: '',
            prjGeneral: '',
            prjTagList: [],
            prjTargetList: [],
            prjAreaList: [],
            prjCover: {
                echoUrl: '',
                commUrl: '',
            },
        }
    }
    return {
        prjId,
        // releaseDate,
        prjForm,
        prjDetail,
        setPrjId,
        getPrjId,
        // setDate,
        // getDate,
        setForm,
        getForm,
        setPrjCover,
        getPrjCover,
        setPrjTagList,
        getPrjTagList,
        removeTag,
        setPrjTargetList,
        getPrjTargetList,
        removeTarget,
        setPrjAreaList,
        getPrjAreaList,
        removeArea,
        //
        getPrjDetail,
        //
        clearData,
    }
},{
    persist: {
        enabled: true
    }
});