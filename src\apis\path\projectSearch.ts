import { http } from "../index";
import type { APIResponse } from "@/utils/type";
export interface taskItem {
  oid: number | null;
  klgName: string;
  areaTitle: string;
  areaCode: string;
  deleted: boolean;
}

export interface params2getAnsList {
  current: number;
  limit: number;
  answerStatus: number;
  answerExplanation: string;
  prjTitle?: string;
}

export interface key2prjList {
  klgCode: string;
  keyword: string;
  prjForm?: string;
  status?: string;
  current: number;
  limit: number;
}
export function getAnswerListApi(
  params: params2getAnsList
): Promise<APIResponse> {
  return http.post("/dutchman-server/answer/getAnswerList", params);
}

// export function getAreaListApi(klgCode:string): Promise<APIResponse> {
//   return http.get {
//     `/tprj/explanation/knowledge/area/query?klgCode = ${klgCode}`
//   }
// }

// 获取项目所属领域数组
export function getAreaListApi(klgCode: string): Promise<APIResponse> {
  return http.get(
    `/tprj/explanation/knowledge/area/query?klgCode=${klgCode}`
  );
}

// 获取知识点列表
export function getKlgListApi(keyword: string): Promise<APIResponse> {
  return http.get(
    `/tprj/explanation/knowledge/query?keyword=${keyword}`
  );
}

//获取项目列表
export function getProjectListApi(param: key2prjList): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: "/tprj/explanation/page",
    data: param,
  });
}
