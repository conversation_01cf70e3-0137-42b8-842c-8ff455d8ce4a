import COS, { type UploadFileItemResult } from "cos-js-sdk-v5";
import { getM3U8Key, getTmpSecretKey } from "@/apis/path/cosBackend";
import { v4 as uuid4 } from "uuid";
import { type Ref, ref } from "vue";
import { ElMessage } from "element-plus";
const prjId = ref<number>(0);
export const setPrjId4cosKey = (newId: number) => {
  prjId.value = newId;
};
const cos = new COS({
  getAuthorization: (options, callback) => {
    getTmpSecretKey(prjId.value).then((res) => {
      if (res.success) {
        callback({
          TmpSecretId: res.data.TmpSecretId,
          TmpSecretKey: res.data.TmpSecretKey,
          SecurityToken: res.data.SecurityToken,
          // StartTime: res.data.StartTime,
          // ExpiredTime: res.data.ExpiredTime,
          StartTime: 1712659517,
          ExpiredTime: 1812659517,
        });
      } else {
        ElMessage.error(res.message);
      }
    });
    // callback({
    //     TmpSecretId: 'AKIDHT9E9CJPdov0MXxngXf7QpGnQZ2Ek5YKCeZXpJNtSF8YXW6J8OOjBU13PbMirfQ6',
    //     TmpSecretKey: 'NTeNXg49W5nDM/GSVhRKY9fniSkNwH0aucE/WDr0RaI=',
    //     SecurityToken:
    //         '5DfTONcFkwOowXodQQtIVL5ZmsxGigqa55074f1ef7bff11a7a2bbce415f9956eFACe3NhbpGOi84ST_J-T33G2qNM_dH_6ymZhEmAx8CWekYM68eNF-nQR9vs2mqe-mX3nJpzVTVzrujv5YMcsPkRiIPM9_9wde05gNgZxGBNhaBTay8hDa4Cp22uAU6meXcyi7iTXs1unKGf7ACcZ0XkUDuiS6tn4A4i4acyG56LG2WAnlIRCh4SiGJdLoLHl36Kz_4aQWUl1B8EhI-GO90OKuo9BO29vQpnGTMXPpTXlCSwHCRuiAJh9kPPNfdBdq-IIscMHuikm4ntlx46cgnMiPxIcPnrHEy-f7hQXDgvs86m7Jse6tP7x2oPVU86ULxqIo_qiTtGkSYlmKHcakXnYpnxSHvl9rXLLlj6-1laynGP4bI3suWngsQPabX7S-b6979m3RTEvhZ37tLhhHJnGnvYSXwgHRHBWDYj1xMWLMWvgXhWaTw4GSgDOYnk7DsVxWM2OB482QFBFmQTgFsdJje4569isv1zoN7lyQJs5UCbJ56VKvEZVAHeyf0OuIHd-cB4xMEKm9uqdcK-ElzTqE8F8rQyv7oAwLxA_VYcSgPj-D0LM45fAyWfETfox',
    //     StartTime: 1712659517, // 时间戳，单位秒，如：1580000000
    //     ExpiredTime: 1812659517, // 时间戳，单位秒，如：1580000000
    // })
  },
});
export const buildKey = (
  date: string,
  prjId: number,
  sectionId?: number,
  fileType?: string,
  fileName?: string
) => {
  // const today = new Date();
  // const year = today.getFullYear();
  // const month = String(today.getMonth() + 1).padStart(2, '0'); // 从0开始，需要加1
  // const day = String(today.getDate()).padStart(2, '0');
  const uniqueId: string = uuid4();
  if (sectionId) {
    return `${date}/${prjId}/${sectionId}/${fileType}/${uniqueId}_${fileName}`;
  } else {
    return `${date}/${prjId}/${uniqueId}_${fileName}`;
  }
};

// 上传任务接口
export interface UploadTask {
  taskId: string;
  cancel: () => void;
  promise: Promise<UploadFileItemResult>;
}

export const sliceUpload = (
  file: File,
  storeKey: string,
  processValue: Ref<number>,
  taskIdRef: Ref<string | undefined>,
  keyRef: Ref<string | undefined>
): UploadTask => {
  let taskReference: any = null;

  const promise = new Promise<UploadFileItemResult>((resolve, reject) => {
    taskReference = cos.uploadFile({
      Bucket: import.meta.env.VITE_COS_BUCKET_NAME,
      Region: import.meta.env.VITE_COS_REGION,
      Key: storeKey,
      Body: file,
      SliceSize: 1024 * 1024 * 5,
      onTaskReady: function (taskId) {
        taskIdRef.value = taskId;
        keyRef.value = storeKey;
      },
      onProgress: function (progressData) {
        processValue.value = Math.floor(progressData.percent * 100);
      },
      onFileFinish: function (err, data, _options) {
        if (err) {
          // 检查是否是用户取消的错误
          if (
            (err as any).error === "task_cancelled" ||
            err.message?.includes("cancel")
          ) {
            reject(new Error("USER_CANCELLED"));
          } else {
            reject(err);
          }
        } else {
          resolve(data);
        }
      },
      // 支持自定义headers 非必须
      Headers: {
        "x-cos-meta-test": 123,
      },
    });
  });

  return {
    taskId: taskIdRef.value || "",
    cancel: () => {
      if (taskReference && taskIdRef.value) {
        cos.cancelTask(taskIdRef.value);
        console.log("已取消上传任务:", taskIdRef.value);
      }
    },
    promise,
  };
};
// 获取视频信息
export const getVideoInfo = (key: string): Promise<any> => {
  const config = {
    // 需要替换成您自己的存储桶信息
    Bucket: import.meta.env.VITE_COS_BUCKET_NAME, // 存储桶，必须
    Region: import.meta.env.VITE_COS_REGION, // 存储桶所在地域，必须字段
  };
  return new Promise((resolve, reject) => {
    cos.request(
      {
        Bucket: config.Bucket, // 存储桶，必须字段
        Region: config.Region, // 存储桶所在地域，必须字段 如 ap-beijing
        Method: "GET", // 固定值，必须
        Key: key, // 存储桶内的媒体文件，必须字段
        Query: {
          "ci-process": "videoinfo", // 固定值，必须
        },
      },
      function (err, data) {
        if (err) {
          reject(err);
        } else {
          resolve(data);
        }
      }
    );
  });
};
// 获取视频url
export const getVideoUrl = (
  bucketName: string,
  bucketRegion: string,
  storeKey: string,
  resultUrl: Ref
) => {
  cos.getObjectUrl(
    {
      Bucket: bucketName, // 填入您自己的存储桶，必须字段
      Region: bucketRegion, // 存储桶所在地域，例如 ap-beijing，必须字段
      Key: storeKey, // 存储在桶里的对象键（例如1.jpg，a/b/test.txt），支持中文，必须字段
      Sign: true, // 获取带签名的对象 URL
      Domain: import.meta.env.VITE_COS_DOMAIN,
      Protocol: "https:",
    },
    function (err, data) {
      if (err) return console.log("cos: getObjectUrl->error: ", err);
      /* url为对象访问 url */
      // const url = data.Url + import.meta.env.VITE_COS_AFTER;
      const url = data.Url;
      /* 复制 downloadUrl 的值到浏览器打开会自动触发下载 */
      const res =
        url +
        (url.indexOf("?") > -1 ? "&" : "?") +
        "response-content-disposition=inline";
      resultUrl.value = res;
    }
  );
  // cos.getObject({
  //     Bucket: bucketName, // 填入您自己的存储桶，必须字段
  //     Region: bucketRegion, // 存储桶所在地域，例如 ap-beijing，必须字段
  //     Key: storeKey, // 存储在桶里的对象键（例如1.jpg，a/b/test.txt），支持中文，必须字段
  //     onProgress: function (progressData) {
  //         console.log('cos onProgress: ', JSON.stringify(progressData));
  //     }
  // }, function(err, data) {
  //     console.log('cos callback: ', err || data.Body);
  //     if (data.Body) resultUrl.value = data.Body;
  // });
};
export const getVideoCut = (storeKey: string, tick: number): Promise<any> => {
  return new Promise((resolve, reject) => {
    cos.request(
      {
        Bucket: import.meta.env.VITE_COS_BUCKET_NAME, // 填入您自己的存储桶，必须字段
        Region: import.meta.env.VITE_COS_REGION, // 存储桶所在地域，例如 ap-beijing，必须字段
        Key: storeKey, // 存储在桶里的对象键（例如1.jpg，a/b/test.txt），支持中文，必须字段
        // Key: '2024-05-11/428/388/video/0aff81da-df6f-493c-a78b-1b35b967d97e_VCG42N1294369918_ic4874aff0f9611efbab6525400222cbe.m3u8', // 存储在桶里的对象键（例如1.jpg，a/b/test.txt），支持中文，必须字段
        Method: "GET",
        Query: {
          "ci-process": "snapshot" /** 固定值，必须 */,
          time: tick /** 截图的时间点，单位为秒，必须 */,
          // width: 0, /** 截图的宽，非必须 */
          // height: 0, /** 截图的高，非必须 */
          // format: 'jpg', /** 截图的格式，支持 jpg 和 png，默认 jpg，非必须 */
          // rotate: 'auto', /** 图片旋转方式，默认为'auto'，非必须 */
          // mode: 'exactframe', /** 截帧方式，默认为'exactframe'，非必须 */
        },
        RawBody: true,
        // 可选返回文件格式为blob
        DataType: "blob",
      },
      function (err, data) {
        if (err) {
          reject(err);
        } else {
          resolve(data);
          // console.log("cos: get cut: " + data)
        }
        // if (data.Body) resultUrl.value = window.URL.createObjectURL(data.Body as Blob) ;
      }
    );
  });
};
export const getVideoSnapshot = (
  bucketName: string,
  bucketRegion: string,
  storeKey: string,
  resultUrl: Ref
) => {
  cos.request(
    {
      Bucket: bucketName, // 填入您自己的存储桶，必须字段
      Region: bucketRegion, // 存储桶所在地域，例如 ap-beijing，必须字段
      Key: storeKey, // 存储在桶里的对象键（例如1.jpg，a/b/test.txt），支持中文，必须字段
      // Key: '2024-05-11/428/388/video/0aff81da-df6f-493c-a78b-1b35b967d97e_VCG42N1294369918_ic4874aff0f9611efbab6525400222cbe.m3u8', // 存储在桶里的对象键（例如1.jpg，a/b/test.txt），支持中文，必须字段
      Method: "GET",
      Query: {
        "ci-process": "snapshot" /** 固定值，必须 */,
        time: 1 /** 截图的时间点，单位为秒，必须 */,
        // width: 0, /** 截图的宽，非必须 */
        // height: 0, /** 截图的高，非必须 */
        // format: 'jpg', /** 截图的格式，支持 jpg 和 png，默认 jpg，非必须 */
        // rotate: 'auto', /** 图片旋转方式，默认为'auto'，非必须 */
        // mode: 'exactframe', /** 截帧方式，默认为'exactframe'，非必须 */
      },
      RawBody: true,
      // 可选返回文件格式为blob
      DataType: "blob",
    },
    function (err, data) {}
  );
};
