/* color palette from <https://github.com/vuejs/theme> */
:root {
  --vt-c-white: #ffffff;
  --vt-c-white-soft: #f8f8f8;
  --vt-c-white-mute: #f2f2f2;

  --vt-c-black: #181818;
  --vt-c-black-soft: #222222;
  --vt-c-black-mute: #282828;

  --vt-c-indigo: #2c3e50;

  --vt-c-divider-light-1: rgba(60, 60, 60, 0.29);
  --vt-c-divider-light-2: rgba(60, 60, 60, 0.12);
  --vt-c-divider-dark-1: rgba(84, 84, 84, 0.65);
  --vt-c-divider-dark-2: rgba(84, 84, 84, 0.48);

  --vt-c-text-light-1: var(--vt-c-indigo);
  --vt-c-text-light-2: rgba(60, 60, 60, 0.66);
  --vt-c-text-dark-1: var(--vt-c-white);
  --vt-c-text-dark-2: rgba(235, 235, 235, 0.64);

  --font-family-text: '阿里巴巴普惠体 3.0 55 L3', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0', sans-serif;
  --font-family-title: "阿里巴巴普惠体 3.0 55", "阿里巴巴普惠体 3.0", sans-serif;
  --text-family: '阿里巴巴普惠体 3.0 55', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0', sans-serif;
  --font-family-info: '阿里巴巴普惠体 3.0 75 SemiBold', '阿里巴巴普惠体 3.0 55 Regular', '阿里巴巴普惠体 3.0', sans-serif;
  --font-family-logo: 'Alimama FangYuanTi VF SemiBold', 'Alimama FangYuanTi VF Regular', 'Alimama FangYuanTi VF', sans-serif;

  --color-primary: #1661AB;   /* 主题色 */
  --color-primary-transparent: rgba(22, 97, 171, 0.5);
  --color-second: #C5D5EA;    /* 辅助色 -hover */
  --color-black: #333333;     /* 主题字色 */
  --color-deep: #666666;      /* 导航次要字色 */
  --color-grey: #999999;      /* 次要字色 */
  --color-light: #F2F2F2;     /* 页面底色、失效字色 */
  --color-boxborder: #DCDFE6FE;
  --color-group-background: #DCDFE6; /* 页面次要元素 */
  --color-invalid: #C0C4CC;   /* 提示字色 */
  --color-line: rgb(216, 225, 233);
  --color-nosrc: #CCCCCC;     /* 阴影色 */

  --color-case-btn: #FF9A6A;
  --color-explain-btn: #ECC32F;
  --color-test-btn:#53C99F;

  --width-content: 1200px;
  --width-less: 1000px;
  --bgcolor-content: rgb(242, 242, 242);
}

/* semantic color variables for this project */
:root {
  --color-background: var(--vt-c-white);
  --color-background-soft: var(--vt-c-white-soft);
  --color-background-mute: var(--vt-c-white-mute);

  --color-border: var(--vt-c-divider-light-2);
  --color-border-hover: var(--vt-c-divider-light-1);

  --color-heading: var(--vt-c-text-light-1);
  --color-text: var(--vt-c-text-light-1);

  --section-gap: 160px;
}

/* @media (prefers-color-scheme: dark) {
  :root {
    --color-background: var(--vt-c-black);
    --color-background-soft: var(--vt-c-black-soft);
    --color-background-mute: var(--vt-c-black-mute);

    --color-border: var(--vt-c-divider-dark-2);
    --color-border-hover: var(--vt-c-divider-dark-1);

    --color-heading: var(--vt-c-text-dark-1);
    --color-text: var(--vt-c-text-dark-2);
  }
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
}


body {
  min-height: 100vh;
  color: var(--color-text);
  background: var(--color-background);
  transition: color 0.5s, background-color 0.5s;
  line-height: 1.6;
  font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu,
    Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  font-size: 15px;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
} */

body {
  min-height: 100vh;
  overflow-x: hidden;
}

/* 全局滚动条样式 */
body::-webkit-scrollbar {
  width: 12px; /* 滚动条宽度 */
  height: 12px; /* 滚动条高度 */
}

body::-webkit-scrollbar-thumb {
  background-color: #c1c1c1; /* 滚动条滑块颜色 */
  border-radius: 6px; /* 滑块圆角 */
}

body::-webkit-scrollbar-track {
  background-color: #f0f0f0; /* 滚动条轨道颜色 */
}