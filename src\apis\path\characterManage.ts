import { http } from '@/apis';
import type {APIResponse} from "@/utils/type";

export function getCharacterList(): Promise<APIResponse>{
    return http.request({
        method: 'get',
        url: `/role/getAll`,
    });
}
export function getCharacterListPage(current:number, limit: number): Promise<APIResponse>{
    return http.request({
        method: 'get',
        url: `/role/getRole?current=${current}&limit=${limit}`,
    });
}
export function deleteCharacter(id: number): Promise<APIResponse> {
    return http.request({
        method: 'get',
        url: `/role/remove/${id}`,
    });
}
export interface params2character {
    name?: string,
    oid?: number,
    rightset?: string
}
export function addCharacter(param: params2character): Promise<APIResponse> {
    return http.request({
        method: 'post',
        url: `/role/save`,
        data: param,
    });
}
export function editCharacter(param: params2character): Promise<APIResponse> {
    return http.request({
        method: 'post',
        url: '/role/edit',
        data: param,
    });
}
export function getCharacterDetails(id: number): Promise<APIResponse> {
    return http.request({
        method: 'get',
        url: `/role/getDetail/${id}`,
    });
}