<script setup lang="ts">
import { watch, ref, onUnmounted } from "vue"
import type { imgUrl } from "@/utils/type"
import MyVideoPlayer from "@/views/common/myVideoPlayer.vue"
import { getVideoCut, getVideoUrl, setPrjId4cosKey } from "@/utils/cos"
import { getM3U8Key } from "@/apis/path/cosBackend"
import { useRoute, useRouter } from "vue-router"
import router from "@/router"
const route = useRoute()
const props = defineProps({
  video: {
    type: String,
    required: true,
  },
  cover: {
    type: Object as () => imgUrl,
    required: true,
  },
  projectId: {
    type: String,
    required: true,
  },
})
const curPrjId = ref()
const mp4VideoKey = ref()
const videoSrc = ref()
const videoCut = ref()
const m3u8Key = ref()
const videoCover = ref<imgUrl>({
  echoUrl: "",
  commUrl: "",
})
const getVideoCutFromCos = () => {
  setPrjId4cosKey(curPrjId.value)
  getVideoUrl(
    import.meta.env.VITE_COS_BUCKET_NAME,
    import.meta.env.VITE_COS_REGION,
    mp4VideoKey.value,
    videoCut
  )
}
const getVideoFromCos = () => {
  setPrjId4cosKey(curPrjId.value)
  getVideoUrl(
    import.meta.env.VITE_COS_BUCKET_NAME,
    import.meta.env.VITE_COS_REGION,
    m3u8Key.value,
    videoSrc
  )
}
const percentage = ref(0)
let timeInterval: NodeJS.Timer | null = null

// 定义一个函数来启动轮询
const startPolling = () => {
  timeInterval = setInterval(() => {
    getM3U8Key(mp4VideoKey.value).then((res) => {
      if (res.success) {
        videoSrc.value = res.data.m3u8Key
        clearInterval(timeInterval)
      } else {
        percentage.value = res.data.progress
      }
    })
  }, 5000)
}

// 轮询函数
const polling4resKey = () => {
  getM3U8Key(mp4VideoKey.value).then((res) => {
    // 成功了就执行一次
    if (res.success) {
      videoSrc.value = res.data.m3u8Key
    }
    // 失败了就进入轮询
    else {
      startPolling()
    }
  })
}

router.beforeEach((to, from, next) => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
  next()
})

watch(
  () => props,
  (newVal, oldValue) => {
    videoCover.value = { ...newVal.cover }
    mp4VideoKey.value = newVal.video
    curPrjId.value = newVal.projectId
    if (newVal.video) {
      polling4resKey()
    }
  },
  { deep: true, immediate: true }
)
</script>

<template>
  <div class="preview-wrapper">
    <div class="video-container">
      <template v-if="videoSrc">
        <my-video-player :video-src="videoSrc"></my-video-player>
      </template>
      <template v-else>
        <img v-if="videoCut" class="video-cut" :src="videoCut" />
        <div style="">
          <el-progress type="dashboard" :percentage="percentage">
            <template #default="{ percentage }">
              <span class="percentage-label">后台已处理</span>
              <span class="percentage-value">{{ percentage }}%</span>
            </template>
          </el-progress>
        </div>
      </template>
    </div>
    <div class="cover-container">
      <img v-if="videoCover.echoUrl" class="pic" :src="videoCover.echoUrl" />
      <el-empty v-else description="暂无封面" />
    </div>
  </div>
</template>

<style scoped>
.preview-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
  height: 360px;

  .video-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 640px;
    height: 360px;
    background-color: var(--color-nosrc);

    .video-cut {
      height: 100%;
      width: 100%;
    }

    .myicon {
      position: absolute;
      /*cursor: pointer;*/
      height: 30px;
    }
  }

  .cover-container {
    width: 480px;
    height: 360px;
    .pic {
      width: 480px;
      height: 360px;
    }
  }
}
</style>
