<script setup lang="ts">
import {onMounted, ref, watch} from "vue";

const props = defineProps({
  type: {
    type: String,
    required: false,
    default: "primary",
  },
  height: {
    type: Number,
    required: false,
    default: 35,
  },
  width: {
    type: Number,
    required: false,
    default: 120,
  },
  clickable: {
    type: Boolean,
    required: false,
    default: true,
  },
});
const enabled = ref(true);
watch(()=>props.clickable,(newValue, oldValue)=>{
  // console.log('clickable: ', newValue);
  enabled.value = newValue;
}, {deep: true, immediate: true});
onMounted(() => {
})
</script>

<template>
  <span style="font-size: 14px;" :class="[type,{'disable':!enabled}]" :style="`height: ${height}px; width: ${width}px`">
    <slot></slot>
  </span>
</template>

<style scoped>
.primary {
  background-color: var(--color-primary);
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  
  &:hover {
    &.disable {
      cursor: not-allowed;
    }
    &:not(.disable) {
      cursor: pointer;
      color: var(--color-primary);
      background-color: var(--color-second);
      border: none;
    }
  }
}
.light {
  border: 1px solid var(--color-primary);
  background-color: white;
  color: var(--color-primary);
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  &:hover {
    color: var(--color-primary);
    background-color: var(--color-second);
    border: none;
  }
}
.group {
  border: 1px solid var(--color-boxborder);
  background-color: white;
  color: var(--color-black);
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  &:hover {
    background-color: var(--color-primary);
    color: white;
    border: none;
  }
}
</style>