<script setup lang="ts">
import { onMounted, reactive, ref, watch } from 'vue';
import type { examineType } from "@/utils/type";
import type { FormRules } from "element-plus";
import MyButton from "@/views/common/myButton.vue";
import MyFlipper from "@/views/common/myFlipper.vue";
import { getExamineInfo, type params2Examine, submitExamine } from "@/apis/path/projectExamine";
import { ElMessage } from "element-plus";
import { decodeData2 } from "@/utils/func";

const props = defineProps({
  prjId: {
    type: Number,
    required: true,
  },
});
const curPrjId = ref();
const examineTableRef = ref();
const ruleFormRef = ref();
const tableData = ref();
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const curProcess = ref();
watch(() => props, (newValue, oldValue) => {
  // 只有放在watch里面才生效，后期可以梳理一下此处的生命周期
  curPrjId.value = newValue.prjId;
  const params = {
    current: currentPage.value,
    limit: pageSize.value,
    id: curPrjId.value
  }
  if(params.id){
      getExamineInfo(params).then((res) => {
      // 这俩字段的后端返回字段是反着的，但是我没有生气，我脾气很好
      formData.link = res.data.list[0].verifierNow;
      formData.auditor = res.data.list[0].verifystepNow;
      tableData.value = res.data.list[0].verifierResult?.map(v => decodeData2(v));
      curProcess.value = res.data.list[0].verifyStepNow;
      total.value = res.data.total;
    })
  }
}, { deep: true, immediate: true });
const formData = reactive<examineType>({
  link: '',
  auditor: '',
  result: false,
  opinion: '',
})
const rules = reactive<FormRules<examineType>>({
  result: [{ required: true, message: '请选择结果', trigger: 'change' }],
  opinion: [{ required: true, message: '请输入意见', trigger: 'blur' }],
})
const handleChangePage = (newPage: number) => {
  currentPage.value = newPage;
}
const handleClose = () => {
  window.close();
}
const handleSubmit = () => {
  // TODO: 表单校验
  if (!ruleFormRef.value) return;
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      console.log('表单校验合法');
      const params: params2Examine = {
        currActionName: curProcess.value,
        projectId: curPrjId.value,
        verifyResult: formData.result ? 1 : 0, // 1:审核通过 0:退回
        verifySuggestion: formData.opinion
      }
      submitExamine(params).then(res => {
        if (res.success) {
          ElMessage.success('审核成功');
          setTimeout(handleClose, 200);
        }
      })
    }
  })
}
// onMounted(() => {

// })
</script>

<template>
  <div class="examine-container">
    <div class="upper-wrapper">
      <div class="floor-wrapper">
        <div class="info-wrapper">
          <div class="info">
            <span class="label">当前环节</span>
            {{ formData.link }}
          </div>
          <div class="info">
            <span class="label">审核人</span>
            {{ formData.auditor }}
          </div>
        </div>
        <div class="btn-group">
          <my-button type="light" @click="handleClose">返回</my-button>
          <my-button @click="handleSubmit">提交</my-button>
        </div>
      </div>
      <el-form ref="ruleFormRef" :model="formData" :rules="rules" @submit.prevent>
        <el-form-item prop="result" label="审核结果">
          <el-radio-group class="radio-bar" v-model="formData.result">
            <!-- element-plus 2.6.0 以后的版本新增了value这个api用于传值，并且计划在3.0.0移除label这个api，如果我们的系统升级了组件库，这个地方要注意一下-->
            <el-radio :label="true">审核通过</el-radio>
            <el-radio :label="false">退回</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="opinion" label="审核意见">
          <el-input v-model="formData.opinion" />
        </el-form-item>
      </el-form>
    </div>
    <div class="line"></div>
    <div style="font-weight: 600; margin: 10px;">审核记录</div>
    <div class="line"></div>
    <el-table ref="examineTableRef" :data="tableData" empty-text="暂无数据">
      <el-table-column prop="createTime" label="时间" width="483"></el-table-column>
      <el-table-column prop="processorName" label="审核人" width="198"></el-table-column>
      <el-table-column prop="actionName" label="审核环节" width="294"></el-table-column>
      <el-table-column prop="auditResult" label="审核结果" width="165"></el-table-column>
    </el-table>
    <my-flipper @change-page="handleChangePage" :current="currentPage" :page-size="pageSize" :total="total"></my-flipper>
  </div>
</template>

<style scoped>
.examine-container {
  display: flex;
  flex-direction: column;
  width: 100%;

  .upper-wrapper {
    padding-bottom: 30px;

    .floor-wrapper {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      width: 100%;

      .info-wrapper {
        display: flex;
        flex-direction: column;

        .info {
          display: flex;
          flex-direction: row;
          font-size: 14px;
          align-items: center;
          color: var(--color-black);

          .label {
            /*直接粘的el-form的lable样式，为了和表单样式的label保持一致*/
            display: inline-flex;
            justify-content: flex-end;
            align-items: flex-start;
            flex: 0 0 auto;
            font-size: 14px;
            color: var(--el-text-color-regular);
            height: 32px;
            line-height: 32px;
            padding: 0 12px 0 0;
            box-sizing: border-box;
          }
        }
      }

      .btn-group {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 270px;
      }
    }

    .el-form-item {
      margin-bottom: 0;
    }

    :deep(.el-input__wrapper) {
      height: 110px;
      align-items: flex-start;
      padding: 10px;
    }
/* 
    &::after {
      content: '';
      height: 1px;
      width: 100%;
      background-color: var(--color-line);
      position: absolute;
      bottom: 0;
    } */
  }

  .radio-bar {
    margin-bottom: 16px;
    width: 237px;
    display: flex;
    justify-content: space-between;

    :deep(.el-radio__input.is-checked .el-radio__inner),
    :deep(.el-radio__inner:hover) {
      border-color: var(--color-grey);
      background: white;
    }

    :deep(.el-radio__inner::after) {
      width: 8px;
      height: 8px;
      background: var(--color-grey);
    }

    :deep(.el-radio__input.is-checked + .el-radio__label) {
      color: var(--color-black);
    }
  }

  .el-table {
    width: 100%;

    &:deep(.el-table__cell) {
      padding: 0;
    }

    &:deep(.cell) {
      justify-content: center;
      display: flex;
      align-items: center;
      min-height: 46px;
    }
  }
}
</style>