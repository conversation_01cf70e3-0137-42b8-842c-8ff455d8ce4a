<script setup lang="ts">
import { onMounted, onUnmounted, provide, ref } from "vue";
import { RouterView, useRouter } from "vue-router";
// import Editor from "ckeditor5-custom-build"
// provide("Editor", Editor);
// 窗口多开处理逻辑

document.addEventListener("visibilitychange", () => {
  if (!document["hidden"]) {
    console.log("出现");
  } else {
    console.log("隐藏");
  }
});

const imgs: Array<HTMLElement> = [];

const observer = new MutationObserver(() => {
  let scripts = Array.prototype.slice.call(
    document.body.getElementsByTagName("script")
  );
  scripts.forEach(function (script) {
    if (!script.type || !script.type.match(/math\/tex/i)) {
      return -1;
    }
    const display = script.type.match(/mode\s*=\s*display(;|\s|\n|$)/) != null;

    const katexElement = document.createElement(display ? "div" : "span");
    katexElement.setAttribute(
      "class",
      display ? "equation" : "inline-equation"
    );
    try {
      katex!.render(script.text.replace(/\s+/g, " "), katexElement, {
        displayMode: display,
      });
    } catch (err) {
      //console.error(err); linter doesn't like this
      katexElement.textContent = script.text;
    }
    script.parentNode.replaceChild(katexElement, script);
  });

  const images = document.querySelectorAll("img");

  images.forEach((img) => {
    if (imgs.includes(img)) {
      return;
    }
    img.addEventListener(
      "dblclick",
      function () {
        const range = document.createRange();
        range.selectNode(this);

        const selection = window.getSelection();
        selection!.removeAllRanges();
        selection!.addRange(range);
      },
      true
    );
    imgs.push(img);
  });
});

onMounted(() => {
  observer.observe(document.body, {
    childList: true,
    subtree: true,
  });
});
onUnmounted(() => {
  observer.disconnect();
});
</script>

<template>
  <router-view></router-view>
</template>

<style scoped>
:deep(img) {
  height: auto;
}
/* el-plus的dialog组件通过teleport渲染 */
* {
  --el-color-primary: var(--color-primary);
}
:deep(.ck.ck-balloon-panel.ck-powered-by-balloon) {
  display: none;
}
:deep(.el-dialog__header) {
  padding: 10px;
  margin-right: 10px;
}

:deep(.el-dialog__body) {
  padding: 10px 34px 20px 40px;
}
:deep(.question-dialog .el-dialog__body) {
  padding: 20px;
}
:deep(.examine-dialog .el-dialog__body) {
  padding: 30px;
}
:deep(.el-overlay) {
  background-color: rgba(0, 0, 0, 0.3);
}
:deep(.el-message-box.el-message-box__btns.el-button) {
  color: red;
}
:deep(.line) {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
}
</style>
<style>
.primary {
  color: var(--color-primary);
  --el-color-primary: var(--color-primary);
}
.tooltip-width {
  /*这个偏移好怪*/
  margin-left: 6px;
  display: flex;
  /* white-space: nowrap; */
  max-width: 300px;
}

/* 不许ckeditor打广告^^ */
.ck-powered-by {
  display: none !important;
}
.ck-balloon-panel_position_border-side_right {
  display: none !important;
}
.el-overlay-dialog {
  &::-webkit-scrollbar {
    position: absolute;
    /*滚动条整体样式*/
    width: 5px;
    /*高宽分别对应横竖滚动条的尺寸*/
    height: 1px;
  }

  &::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 5px;
    background: var(--color-grey);
  }
}
</style>
