<script setup lang="ts">
import axios from "axios"
import { docCookies } from "@/utils/func"
import "vue-cropper/dist/index.css"
import { VueCropper } from "vue-cropper"
import { ref, reactive, onMounted, watch } from "vue"
import { editingPrjStore } from "@/stores/editingPrj"
import { Plus, Edit, Remove } from "@element-plus/icons-vue"
import { ElMessage, type UploadProgressEvent } from "element-plus"
import { removeVideoCoverApi } from "@/apis/path/createProject"
import { createProject, uploadImage } from "@/apis/path/createProject"
import type {
  UploadInstance,
  UploadProps,
  UploadRawFile,
  UploadFile,
} from "element-plus"
import { genFileId } from "element-plus"
import MyButton from "@/views/common/myButton.vue"
import type { prjInfoType } from "@/utils/type"
const editingPrj = editingPrjStore()
const emit = defineEmits(["sendImgUrl", "sendPrjId"])
const longUrl = ref("") //回显
const shortUrl = ref("") //发送给后端
const prjId = ref()
const sectionId = ref()
const upload = ref()
const prjForm = ref()
const noUpload = ref(false)
const props = defineProps({
  PrjId: {
    type: Number,
    required: true,
  },
  SectionId: {
    type: Number,
    required: false,
  },
  LongUrl: {
    type: String,
    required: true,
  },
  projectForm: {
    type: String,
    required: false,
  },
  canRemove: {
    type: Boolean,
    required: false,
    default: false,
  },
})
watch(
  () => props,
  (newValue, oldValue) => {
    // console.log('imgUploader: watched\n' + JSON.stringify(newValue, null, 2))
    prjId.value = newValue.PrjId
    sectionId.value = newValue.SectionId
    longUrl.value = newValue.LongUrl
    prjForm.value = newValue.projectForm
  },
  { deep: true, immediate: true }
)
onMounted(() => {})
const handleExceed: UploadProps["onExceed"] = (files) => {
  upload.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  upload.value!.handleStart(file)
}

let isLoading = ref(false)
// 图片裁剪
const centerDialogVisible = ref(false)
//获取裁剪图片组件
const cropper = ref()
// 图片裁剪各项参数
const option = reactive({
  img: "", // 裁剪图片的地址
  info: true, // 裁剪框的大小信息
  outputSize: 1, // 裁剪生成图片的质量
  outputType: "jpg", // 裁剪生成图片的格式
  canScale: true, // 图片是否允许滚轮缩放
  autoCrop: true, // 是否默认生成截图框
  canMoveBox: true, // 截图框能否拖动
  canMove: true, // 上传图片是否可以移动
  fixedBox: false, // 固定截图框大小 不允许改变
  fixed: true, // 是否开启截图框宽高固定比例
  fixedNumber: [1.333, 1], // 截图框的宽高比例
  full: false, // 是否输出原图比例的截图
  original: false, // 上传图片按照原始比例渲染
  centerBox: true, // 截图框是否被限制在图片里面
  infoTrue: true, // true 为展示真实输出图片宽高 false 展示看到的截图框宽高
})
// 点击确认
const uploadImg = () => {
  isLoading.value = true
  //上传图片并且点击了确认按钮 异步才能获取数据
  cropper.value.getCropBlob(async (data: Blob) => {
    let formData = new FormData()
    formData.append("file", data)
    formData.append("prjId", prjId.value.toString())
    if (sectionId.value) {
      formData.append("sectionId", sectionId.value.toString())
    }
    const res = await axios({
      method: "post",
      headers: {
        "Access-Control-Allow-Origin": "*",
        token: docCookies.getItem("token"),
      }, // 请求头
      url: "/wellerman-service/cos/file/imageCosUpload", //请求地址
      data: formData,
    })
    if (res.data.success) {
      shortUrl.value = res.data.data.shortUrl
      longUrl.value = res.data.data.longUrl
      noUpload.value = true
      emit("sendImgUrl", shortUrl.value, longUrl.value)
    }
    isLoading.value = false
  })
  centerDialogVisible.value = false
}
const handleUpload = () => {
  if (!prjId.value) {
    createProject(parseInt(prjForm.value)).then((res) => {
      if (res.success) {
        prjId.value = res.data.projectID
        emit("sendPrjId", prjId.value)
        uploadImg()
      }
    })
  } else {
    uploadImg()
  }
}
const upLoadFile = ref()
const handleChange = (UploadFile: UploadFile) => {
  upLoadFile.value = UploadFile
  centerDialogVisible.value = true
  //这里将图片转化为base64位格式为图片进行裁剪
  const reader = new FileReader()
  reader.onload = (e) => {
    //将图片绑定到裁剪组件的img中
    option.img = e.target.result
  }
  //拿到图片的base64
  reader.readAsDataURL(UploadFile.raw as UploadRawFile)
}
const handleRemove = async () => {
  removeVideoCoverApi(sectionId.value.toString()).then((res)=>{
    if(res.success) {
      ElMessage.success("删除成功")
      longUrl.value = ''
    }else{
      ElMessage.error(res.message)
    }
  })
}
</script>

<template>
  <!-- action属性的值为图片上传的地址 -->
  <el-upload
    ref="upload"
    action=""
    list-type="picture-card"
    :limit="1"
    :show-file-list="false"
    :on-change="handleChange"
    :on-exceed="handleExceed"
    :auto-upload="false"
    accept=".jpg,.png,.jpeg"
  >
    <span class="showImg">
      <img v-if="longUrl" :src="longUrl" class="miniPic" />
    </span>

    <el-icon v-if="!longUrl"><Plus /></el-icon>
    <span v-if="!longUrl" style="color: var(--color-group-background)"
      >上传图片</span
    >
    <el-icon v-if="longUrl"><Edit /></el-icon>
    <span class="showBack" v-if="props.canRemove && longUrl">
      <el-icon @click.stop="handleRemove" class="remove-btn"><Delete /></el-icon>
    </span>
  </el-upload>

  <el-dialog
    :close-on-click-modal="false"
    v-model="centerDialogVisible"
    title="裁剪封面"
    class="dialog"
    align-center
  >
    <div class="cropper-content">
      <div class="cropper" style="text-align: center">
        <vueCropper
          ref="cropper"
          :img="option.img"
          :outputType="option.outputType"
          :info="true"
          :full="option.full"
          :canMoveBox="option.canMoveBox"
          :original="option.original"
          :autoCrop="option.autoCrop"
          :fixed="option.fixed"
          :fixedNumber="option.fixedNumber"
          :centerBox="option.centerBox"
          :infoTrue="option.infoTrue"
          :fixedBox="option.fixedBox"
        />
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <my-button type="light" @click="centerDialogVisible = false"
          >取消</my-button
        >
        <my-button type="primary" @click="handleUpload"> 确认 </my-button>
      </span>
    </template>
  </el-dialog>
</template>

<style scoped>
:deep(.el-upload-list__item) {
  width: 240px;
  height: 180px;
}
:deep(.el-upload--picture-card) {
  height: 180px;
}


.showImg {
  position: absolute;
  top: 0;
  .miniPic {
    position: relative;
    width: 239px;
    height: 180px;
    border-radius: 5px;
  }
}
.showBack {
  position: absolute;
  top: 0;
  width: 239px;
  height: 180px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  flex-direction: row;
  justify-content: center;
  font-size: 20px;
  color: #FFFFFFaa;
  .remove-btn {
    padding: 2px;
    border-radius: 3px;
  position: absolute;
  top: 5px;
  right: 5px;
  cursor: pointer;
  background-color: #33333355;
  &:hover {
    background-color: #333333AA;
  }
}


  .miniPic {
    position: relative;
    width: 239px;
    height: 180px;
    border-radius: 5px;
  }
}
.cropper-content {
  .cropper {
    width: auto;
    height: 350px;
    .handle_btn {
      display: flex;
      display: -webkit-flex;
      justify-content: space-between;
      padding: 10px 300px;
      box-sizing: border-box;
    }
  }
}
.dialog {
  width: 900px;
}
.dialog-footer {
  display: flex;
  justify-content: center;
  padding: 0 220px;
  gap: 40px;
}
</style>
