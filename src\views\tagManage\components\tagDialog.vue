<script setup lang="ts">
import {onMounted, ref} from "vue";
import {ElMessage} from "element-plus";
import type {params2tag} from "@/apis/path/tagManage";
import MyButton from "@/views/common/myButton.vue";

const emit = defineEmits(['submitAdd', 'submitEdit']);
const isConfigDialogShow = ref(false);
const dialogTitle = ref('');
const tagName = ref('');
const tagOrder = ref();
const isValid = ref(0);
const curMode = ref(0);
const editId = ref();
const showDialog = (id?: number, name?: string, order?: number) => {
  if (id) {
    curMode.value = 2;
    editId.value = id;
    tagName.value = name as string;
    tagOrder.value = order;
  }
  else {
    curMode.value = 1;
  }
  // curMode: 1新建 | 2编辑
  switch (curMode.value) {
    case 1: dialogTitle.value = '新建标签';
      break;
    case 2: dialogTitle.value = '编辑标签';
      break;
    default: ElMessage.error('初始化弹窗组件出错');
      break;
  }
  isConfigDialogShow.value = true;
}
const handleSubmit = () => {
  // TODO:改成表单验证，这个elmessage老是坏
  if (!tagName.value) {
    ElMessage.warning('请输入标签名称');
    return;
  }
  if (!tagOrder.value) {
    ElMessage.warning('请输入标签顺序');
    return;
  }
  const param = ref<params2tag>({
    orderNum: tagOrder.value,
    isValid: isValid.value,
    tagName: tagName.value,
    oid: editId.value,
  });
  switch (curMode.value) {
    case 1: emit('submitAdd', param.value);
      break;
    case 2: emit('submitEdit', param.value);
      break;
    default: ElMessage.error('提交出错');
      break;
  }
  handleClose();
}
const handleClose = () => {
  isConfigDialogShow.value = false;
}
onMounted(() => {
});
defineExpose({
  showDialog
});
</script>

<template>
  <el-dialog
      :close-on-click-modal="false"
      v-model="isConfigDialogShow"
      width="596"
  >
    <template #header="{titleId}">
      <div :id="titleId" class="title">
        {{dialogTitle}}
      </div>
    </template>
    <div class="content-container">
      <div class="content-line">
        <span>标签名称</span>
        <el-input class="input" v-model="tagName" placeholder="请输入标签名称"></el-input>
      </div>
      <div class="content-line">
        <span>选择顺序</span>
        <el-input-number v-model="tagOrder" class="input" :min="1" placeholder="请选择标签顺序"/>
      </div>
      <div class="content-line">
        <span>是否启用</span>
        <span class="switch">
          <el-switch v-model="isValid" active-value="1" inactive-value="0"/>
        </span>
      </div>
    </div>
    <template #footer>
      <div class="footer">
        <div class="btn-group">
          <my-button type="light" @click="handleClose">关闭</my-button>
          <my-button @click="handleSubmit">确认</my-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
/* el-dialog的渲染是通过teleport直接挂到body下边，在这里写样式不生效，所以把样式写到全局(App.vue)了 */
.title {
  font-size: 16px;
  font-family: var(--font-family-text);
}
.content-container {
  font-family: var(--font-family-text);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .content-line {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 35px;
    margin-bottom: 20px;
    .switch {
      width: 420px;
      display: flex;
      justify-content: flex-start;
      margin-left: 35px;
    }
    .input{
      --el-color-primary: var(--el-border-color);
      margin-left: 35px;
      width: 420px;
    }
  }
}
.footer {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
  .btn-group {
    display: flex;
    justify-content: space-between;
    width: 280px;
  }
}
</style>