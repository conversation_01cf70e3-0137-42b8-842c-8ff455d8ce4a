<script setup lang="ts">
import VideoUploader from "@/views/createProject/step2/videoUploader.vue";
import VideoLecture from "@/views/createProject/step2/videoLecture.vue";
import { reactive, watch, ref, onMounted, onUnmounted, nextTick } from "vue";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import type { lectureType, videoChapterType } from "@/utils/type";
import ImgUpload from "@/views/createProject/components/imgUpload.vue";
import {
  getVideoSection,
  type params4submitVideoSection,
  submitVideoSection,
} from "@/apis/path/createProject";
import { emitter } from "@/utils/emitter";
import { Event } from "@/types/event";
import { convertMathFormulas, revertMathScriptsToMd } from "@/utils/latexUtils";

// 本组件进行前后端通信
const emits = defineEmits(["save:chapter"]);
const props = defineProps({
  chapter: Object,
  showTitle: {
    type: Boolean,
    default: false,
  },
  projectId: Number,
  releaseDate: {
    type: String,
    required: true,
  },
});
const rules = reactive<FormRules<videoChapterType>>({
  sectionTitle: [
    { required: true, message: "请填写小节标题", trigger: "blur" },
  ],
  videoFlag: [{ required: true, message: "请上传视频", trigger: "blur" }],
  // videoCover: [{ required: true, validator: checkCoverUrl, trigger: 'blur' }],
  videoKey: [{ required: true, message: "请上传视频", trigger: "blur" }],
  lectureFlag: [{ required: true, message: "请上传视频讲稿", trigger: "blur" }],
});
// const secName = ref();
const secCount = ref();
const ruleFormRef = ref<FormInstance>();

// 标题双模式编辑器相关变量
const sectionTitleEditMode = ref(false);
const sectionTitleInputRef = ref();
const ruleForm = reactive<videoChapterType>({
  sectionId: -1,
  sectionTitle: "",
  videoFlag: "",
  lectureFlag: "",
  videoCover: {
    echoUrl: "",
    commUrl: "",
  },
  videoKey: "",
  lecture: [
    {
      beginning: false,
      id: 0,
      time: "",
      content: "",
    },
  ],
});

const initData = () => {
  if (ruleForm.sectionId && ruleForm.sectionId !== -1) {
    getVideoSection(ruleForm.sectionId)
      .then((res) => {
        if (res.success) {
          ruleForm.videoCover.echoUrl = res.data.list[0].longImageUrl;
          ruleForm.videoCover.commUrl = res.data.list[0].shortImageUrl;
          ruleForm.videoKey = res.data.list[0].longVideoUrl;
          ruleForm.videoFlag = res.data.list[0].longVideoUrl;
          ruleForm.lecture = res.data.list[0].speechText;
          ruleForm.lectureFlag =
            res.data.list[0].speechText.length === 0 ? "" : "true";
          ruleForm.sectionTitle = res.data.list[0].sectionTitle
            ? res.data.list[0].sectionTitle
            : "默认标题";

          // 初始化标题编辑器状态
          if (ruleForm.sectionTitle && ruleForm.sectionTitle.trim()) {
            ruleForm.sectionTitle = revertMathScriptsToMd(
              ruleForm.sectionTitle
            );
            sectionTitleEditMode.value = false; // 有内容时默认显示预览模式
          } else {
            sectionTitleEditMode.value = true; // 无内容时默认显示编辑模式
          }
        } else {
          ElMessage.error(res.message);
        }
      })
      .catch((error) => {
        console.error("Error fetching video section:", error);
      });
  }
};
watch(
  () => props,
  (newValue) => {
    if (newValue.chapter) {
      // 先设置基本的 chapter 信息
      Object.assign(ruleForm, newValue.chapter);
      // 检查 sectionId 是否为 -1，如果不是，则执行 initData 获取详细数据
      if (ruleForm.sectionId !== -1) {
        initData();
      }
    }
  },
  { deep: true, immediate: true }
);

const receiveVideoKey = (videoKey: string) => {
  // ruleForm.videoKey = videoKey;
  ruleForm.videoFlag = videoKey;
  // console.log('receiveVideoKey: ' + videoKey);
};
const receiveCover = (CommUrl: string, EchoUrl: string) => {
  // commUrl: 前后端通信时的图片链接(short)
  // echoUrl: 前端展示的图片链接(long)
  const newCover = {
    echoUrl: EchoUrl,
    commUrl: CommUrl,
  };
  ruleForm.videoCover = newCover;
};
const receiveLecture = (lectureList: lectureType[]) => {
  ruleForm.lecture = lectureList;
  ruleForm.lectureFlag = "true";
};

// 标题编辑器相关方法
const handleSectionTitleBlur = () => {
  if (ruleForm.sectionTitle.trim()) {
    sectionTitleEditMode.value = false;
  }
};

const handleSectionTitlePreviewClick = () => {
  sectionTitleEditMode.value = true;
  nextTick(() => {
    if (sectionTitleInputRef.value) {
      ruleForm.sectionTitle = revertMathScriptsToMd(ruleForm.sectionTitle);
      sectionTitleInputRef.value.focus();
    }
  });
};

const handleSectionTitleFocus = () => {
  sectionTitleEditMode.value = true;
};
// const setSectionName = (newSecName: string) => {
//   ruleForm.chapterName = newSecName
// }
// const handleSaveDraftSection = () => {
//   if (secId.value == undefined || secCount.value == undefined) return
//   const param: params4submitVideoSection = {
//     chapterNum: secCount.value,
//     sectionId: secId.value,
//     sectionTitle: ruleForm.chapterName,
//   }
//   submitVideoSection(param)
//     .then((res) => {
//       if (res.success) {
//         // ElMessage.success(res.message);
//         // 表明本次保存草稿结束，重置父组件中的saveSectionId
//         emits("saveSectionDraftDone", true)
//       } else {
//         ElMessage.error(res.message)
//         emits("saveSectionDraftDone", false)
//       }
//     })
//     .catch()
// }
// const handleSaveSection = () => {
//   if (secId.value == undefined || secCount.value == undefined) return
//   if (!ruleFormRef.value) {
//     emits("saveSectionDone", false)
//     return
//   }
//   ruleFormRef.value.validate((valid) => {
//     if (valid) {
//       console.log("表单校验合法")
//       const param: params4submitVideoSection = {
//         chapterNum: secCount.value,
//         sectionId: secId.value,
//         sectionTitle: ruleForm.chapterName,
//       }
//       submitVideoSection(param)
//         .then((res) => {
//           if (res.success) {
//             // ElMessage.success(res.message);
//             // 表明本次校验保存结束，父组件中应该跳转到下一步
//             emits("saveSectionDone", true)
//           } else {
//             ElMessage.error(res.message)
//             emits("saveSectionDone", false)
//           }
//         })
//         .catch()
//     } else {
//       console.log("表单校验失败")
//       emits("saveSectionDone", false)
//     }
//   })
// }

// 清空文稿
const handleCleanLecture = (success: boolean) => {
  if (success) {
    ruleForm.lecture = [];
    ruleForm.lectureFlag = "";
  }
};
// 提交
const saveSubmitChapter = (): Promise<Boolean> => {
  return new Promise((resolve, reject) => {
    // 构造参数
    const param: params4submitVideoSection = {
      chapterNum: secCount.value,
      sectionId: ruleForm.sectionId,
      sectionTitle: ruleForm.sectionTitle,
    };

    // 调用 submitVideoSection 并处理结果
    submitVideoSection(param)
      .then((res) => {
        if (res.success) {
          resolve(true);
        } else {
          resolve(false);
        }
      })
      .catch((error) => {
        reject(error);
      });
  });
};

// 提交 or 草稿
const handleSaveChapter = (submit?: boolean) => {
  return new Promise<Boolean>((resolve, reject) => {
    if (!ruleFormRef.value) {
      reject(new Error("ruleFormRef is not defined"));
      return;
    }
    if (submit) {
      ruleFormRef.value.validate((valid) => {
        if (valid) {
          saveSubmitChapter()
            .then(() => {
              resolve(true);
            })
            .catch((error) => {
              reject(error);
            });
        }
      });
    } else {
      saveSubmitChapter()
        .then(() => {
          resolve(true);
        })
        .catch((error) => {
          reject(error);
        });
    }
  });
};

const handleSave = (param: { status: number; id: number }) => {
  if (param.id == ruleForm.sectionId) {
    handleSaveChapter(true).then(() => {
      emits("save:chapter", param);
    });
  }
};

onMounted(() => {
  emitter.on(Event.SAVE_CHAPTER, handleSave);
});
onUnmounted(() => {
  emitter.off(Event.SAVE_CHAPTER, handleSave);
});
</script>

<template>
  <div class="main-wrapper">
    <el-form
      class="form"
      ref="ruleFormRef"
      :model="ruleForm"
      :rules="rules"
      @submit.prevent
    >
      <el-form-item
        prop="sectionTitle"
        style="width: 100%"
        v-if="props.showTitle"
      >
        <!-- 编辑模式：显示输入框 -->
        <el-input
          v-if="sectionTitleEditMode"
          ref="sectionTitleInputRef"
          v-model="ruleForm.sectionTitle"
          placeholder="请输入标题，支持数学公式"
          @blur="handleSectionTitleBlur"
          @focus="handleSectionTitleFocus"
          maxlength="256"
          show-word-limit
        />

        <!-- 预览模式：显示渲染结果 -->
        <div
          v-else-if="ruleForm.sectionTitle"
          class="preview-container"
          style="width: 100%"
          @click="handleSectionTitlePreviewClick"
        >
          <div
            class="preview"
            v-html="convertMathFormulas(ruleForm.sectionTitle)"
          ></div>
          <div class="preview-hint">点击编辑，支持latex公式</div>
        </div>

        <!-- 空状态：显示输入框 -->
        <el-input
          v-else
          ref="sectionTitleInputRef"
          v-model="ruleForm.sectionTitle"
          placeholder="请输入标题，支持数学公式"
          @blur="handleSectionTitleBlur"
          @focus="handleSectionTitleFocus"
          maxlength="128"
        />
      </el-form-item>
      <el-form-item class="video-upload" prop="videoFlag">
        <VideoUploader
          @sendVideoKey="receiveVideoKey"
          :videoKey="ruleForm.videoKey"
          :project-id="props.projectId || 0"
          :section-id="ruleForm.sectionId || 0"
          :release-date="props.releaseDate"
        ></VideoUploader>
      </el-form-item>
      <el-form-item label="视频封面" prop="videoCover">
        <div class="cover-content">
          <ImgUpload
            @send-img-url="receiveCover"
            :PrjId="props.projectId || 0"
            :SectionId="ruleForm.sectionId || 0"
            :LongUrl="ruleForm.videoCover.echoUrl"
            :can-remove="true"
          ></ImgUpload>
          <div class="tip">
            视频封面格式为jpeg,png,文件大小不超过2MB,建议长宽比为4:3 。
          </div>
        </div>
      </el-form-item>
      <el-form-item label="视频文稿" prop="lectureFlag">
        <video-lecture
          @sendLecture="receiveLecture"
          @cleanLecture="handleCleanLecture"
          :lecture="ruleForm.lecture"
          :project-id="props.projectId"
          :section-id="ruleForm.sectionId"
          style="padding-left: 10px"
        ></video-lecture>
      </el-form-item>
    </el-form>
  </div>
</template>

<style scoped>
.main-wrapper {
  width: 100%;
  display: flex;
  .form {
    width: 100%;
    .el-form-item:not(.video-upload) {
      align-items: flex-start;
      flex-direction: column;
    }
    :deep(.el-form-item__content) {
      width: 100%;
    }
    .cover-content {
      display: flex;
      flex-direction: column;
      padding-left: 10px;
      .tip {
        font-size: 12px;
        color: var(--color-grey);
      }
    }

    :deep(.el-upload--picture-card) {
      width: 239px;
      height: 180px;
    }
  }
}
:deep(.el-input) {
  --el-input-height: 35px;
}
</style>
