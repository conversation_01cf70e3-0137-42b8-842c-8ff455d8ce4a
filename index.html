<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="src/assets/favicon.ico" />
    <!-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.11.1/dist/katex.min.css"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1" />
    <!-- 配置MathJax -->
    <!-- <script src="https://cdn.bootcdn.net/ajax/libs/mathjax/3.2.2/es5/a11y/assistive-mml.js"></script> -->
    <!-- <script src="https://cdn.bootcss.com/mathjax/2.7.0/MathJax.js?config=TeX-AMS-MML_HTMLorMML"></script> -->
    <!-- <script id="MathJax-script" src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.11.1/dist/katex.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.11.0/dist/contrib/mhchem.min.js"></script>
    <script src="build/ckeditor.js"></script> -->
    <link rel="stylesheet" href="./katex.min.css" />

    <!-- The loading of KaTeX is deferred to speed up page rendering -->
    <script defer src="./katex.min.js" type="module"></script>

    <!-- To automatically render math in text elements, include the auto-render extension: -->
    <script
      defer
      type="module"
      src="./contrib/auto-render.min.js"
      onload="renderMathInElement(document.body);"
    ></script>
    <script defer src="./contrib/mhchem.min.js" type="module"></script>
    <script src="./contrib/copy-tex.min.js" type="module"></script>
    <title>PBL中心</title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
