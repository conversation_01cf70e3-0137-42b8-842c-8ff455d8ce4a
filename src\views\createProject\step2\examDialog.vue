<script setup lang="ts">
import MyButton from "@/views/common/myButton.vue";
import {reactive, ref} from "vue";
import {examType, examTypeDict} from "@/utils/constant";
import type {examSectionType} from "@/utils/type";
import {findKeyByValue} from "@/utils/func";
import {type FormInstance, type FormRules} from "element-plus";

const emit = defineEmits(['submit']);
const editIndex = ref();
const isDialogShow = ref(false);
const dialogTitle = ref();
const examData = ref<examSectionType>({
  id: -1,
  examAnswer: '',
  examChoices: [''],
  examExplanation: '',
  examTitle: '',
  examType: examType.blank,
});
const rules4others = reactive<FormRules<typeof examData>>({
  examAnswer: [{ required: true, message: '请输入答案', trigger: 'blur' }],
  // examChoices: [{ required: true, message: '请输入', trigger: 'blur' }],
  examExplanation: [{ required: true, message: '请输入解释', trigger: 'blur' }],
  examTitle: [{ required: true, message: '请输入题干', trigger: 'blur' }],
})
const rules4select = reactive<FormRules<typeof examData>>({
  examAnswer: [{ required: true, message: '请选择答案', trigger: 'blur' }],
  examChoices: [{ required: true, message: '请添加选项', trigger: 'blur' }],
  examExplanation: [{ required: true, message: '请输入解释', trigger: 'blur' }],
  examTitle: [{ required: true, message: '请输入题干', trigger: 'blur' }],
})
const ruleFormRef = ref<FormInstance>();
const showDialog = (type: examType, data?: examSectionType, editIdx?: number) => {
  examData.value.examType = type;
  if (data) {
    examData.value = {...data};
    editIndex.value = editIdx;
  }
  dialogTitle.value = findKeyByValue(type, examTypeDict);
  isDialogShow.value = true;
}
const initExamData = () => {
  editIndex.value = undefined;
  examData.value = {
    id: -1,
    examAnswer: '',
    examChoices: [''],
    examExplanation: '',
    examTitle: '',
    examType: examType.blank,
  };
}
const handleAddChoice = () => {
  examData.value.examChoices.push('');
}
const handleSelect = (ansChoice: string) => {
  examData.value.examAnswer = ansChoice;
}
const handleDeleteChoice = (index: number) => {
  examData.value.examChoices.splice(index, 1);
}
const handleClose = () => {
  initExamData();
  isDialogShow.value = false;
}
const handleSubmit = () => {
  if (!ruleFormRef.value) return;
  ruleFormRef.value.validate((valid) => {
    if (valid) {
      console.log('表单验证成功')
      if (editIndex.value !== undefined) {
        emit('submit', examData.value, editIndex.value);
      }
      else {
        emit('submit', examData.value);
      }
      handleClose();
    } else {
      console.log('表单验证失败')
      return false;
    }
  })
}
defineExpose({
  showDialog,
})
</script>

<template>
  <el-dialog v-model="isDialogShow"
             :close-on-click-modal="false"
             width="596">
    <template #header="{titleId}">
      <div :id="titleId" class="title">
        {{dialogTitle}}
      </div>
    </template>
    <div class="content-container">
      <el-form
        ref="ruleFormRef"
        :model="examData"
        :rules="examData.examType == examType.select ? rules4select : rules4others">
        <div class="stem-container">
          <span class="stem-title">
            请编辑题目：
          </span>
          <el-form-item prop="examTitle">
            <el-input class="input" v-model="examData.examTitle" placeholder="请输入题干"></el-input>
          </el-form-item>
        </div>
        <div class="ans-container">
          <template v-if="examData.examType == examType.select">
            <span class="choice-title">
              <span class="icon" @click="handleAddChoice">+</span>
              增加选项
            </span>
            <el-form-item prop="examChoices">
              <div class="choiceList">
                <span class="choice-wrapper" v-for="(c, idx) in examData.examChoices" :key="idx">
                  <span class="icon" @click="handleDeleteChoice(idx)">-</span>
                  <el-input class="choice" v-model="examData.examChoices[idx]" placeholder="请输入选项"></el-input>
                  <el-checkbox @change="handleSelect(c)" style="margin-left: 5px">答案</el-checkbox>
                </span>
              </div>
            </el-form-item>
            <el-form-item prop="examExplanation">
              <el-input v-model="examData.examExplanation" type="textarea" style="margin-top: 55px;" class="explanation input" placeholder="请输入答案解释"></el-input>
            </el-form-item>
          </template>
          <template v-if="examData.examType == examType.blank">
            <div class="blank-wrapper">
              <el-form-item prop="examAnswer">
                <el-input v-model="examData.examAnswer" class="flatter-answer" placeholder="请输入参考答案"></el-input>
              </el-form-item>
              <el-form-item prop="examExplanation">
                <el-input v-model="examData.examExplanation" type="textarea" class="explanation input" placeholder="请输入答案解释"></el-input>
              </el-form-item>
            </div>
          </template>
          <template v-if="examData.examType == examType.judge">
            <el-form-item prop="examAnswer">
              <el-radio-group class="radio-bar" v-model="examData.examAnswer">
                <el-radio label="1">√</el-radio>
                <el-radio label="0">×</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item prop="examExplanation">
              <el-input v-model="examData.examExplanation" type="textarea" class="explanation input" placeholder="请输入答案解释"></el-input>
            </el-form-item>
          </template>
          <template v-if="examData.examType == examType.qa">
            <el-form-item prop="examAnswer">
              <el-input v-model="examData.examAnswer" class="answer input" placeholder="请输入参考答案"></el-input>
            </el-form-item>
            <el-form-item prop="examExplanation">
              <el-input v-model="examData.examExplanation" class="flatter-explanation input" placeholder="请输入答案解释"></el-input>
            </el-form-item>
          </template>
        </div>
      </el-form>
    </div>
    <template #footer>
      <div class="footer">
        <div class="btn-group">
          <my-button type="light" @click="handleClose">关闭</my-button>
          <my-button @click="handleSubmit">确认</my-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
/* TODO: 这个结构乱的逆天，我好像没睡醒一样，一定给他改了 */
/* el-dialog的渲染是通过teleport直接挂到body下边，在这里写样式不生效，所以把样式写到全局(App.vue)了 */
.title {
  font-size: 16px;
  font-family: var(--font-family-text);
}
.input{
  --el-color-primary: var(--el-border-color);
  width: 536px;
}
.content-container {
  /*--el-color-primary: var(--color-primary);*/
  font-family: var(--font-family-text);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .stem-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 20px;
    .stem-title {
      font-size: 14px;
      line-height: 14px;
      font-family: var(--font-family-title);
      margin-bottom: 16px;
    }
    :deep(.el-input__wrapper) {
      border-radius: 0;
    }
  }
  .ans-container {
    :deep(.el-input__wrapper) {
      padding: 10px;
      align-items: flex-start;
      border-radius: 0;
    }
    .radio-bar {
      margin-bottom: 16px;
      width: 155px;
      display: flex;
      justify-content: space-between;
      :deep(.el-radio__input.is-checked .el-radio__inner),
      :deep(.el-radio__inner:hover) {
        border-color: var(--color-grey);
        background: white;
      }
      :deep(.el-radio__inner::after) {
        width: 8px;
        height: 8px;
        background: var(--color-grey);
      }
      :deep(.el-radio__input.is-checked + .el-radio__label) {
        color: var(--color-black);
      }
    }
    .icon {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 16px;
      width: 16px;
      border-radius: 8px;
      background-color: var(--color-primary);
      color: white;
      font-size: 16px;
      margin-right: 10px;
      font-weight: 700;
      cursor: pointer;
    }
    .choice-title {
      margin-bottom: 5px;
      display: flex;
      flex-direction: row;;
      align-items: center;
    }
    .choiceList {
      max-height: 160px;
      overflow-y: auto;
      &::-webkit-scrollbar {
        position: absolute;
        /*滚动条整体样式*/
        width: 5px;
        /*高宽分别对应横竖滚动条的尺寸*/
        height: 1px;
      }
      &::-webkit-scrollbar-thumb {
        /*滚动条里面小方块*/
        border-radius: 5px;
        background: var(--color-grey);
      }
      .choice-wrapper {
        display: flex;
        flex-direction: row;
        align-items: center;
        .choice {
          height: 35px;
          line-height: 35px;
          margin-bottom: 5px;
          --el-color-primary: var(--el-border-color);
          width: 471px;
        }
        :deep(.el-checkbox__label) {
          font-weight: 400;
          padding-left: 5px;
        }
        :deep(.el-input__wrapper) {
          padding: 0 10px;
        }
        :deep(.el-input__inner) {
          height: 100%;
        }
      }
    }
    .explanation {
      height: 166px;
      &:deep(.el-textarea__inner) {
        display: flex;
        height: 100%;
      }
    }
    .answer {
      height: 110px;
      margin-bottom: 20px;
    }
    .flatter-explanation {
      height: 110px;
    }
    .blank-wrapper {
      .flatter-answer {
        height: 35px;
        line-height: 35px;
        margin-bottom: 20px;
        --el-color-primary: var(--el-border-color);
      }
      :deep(.el-input__wrapper) {
        padding: 0 10px;
      }
    }
  }
}
.footer {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
  .btn-group {
    display: flex;
    justify-content: space-between;
    width: 280px;
  }
}
</style>