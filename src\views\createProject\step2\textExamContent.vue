<script setup lang="ts">

import MyButton from "@/views/common/myButton.vue";
import ExamLine from "@/views/createProject/components/examLine.vue";
import ExamDialog from "@/views/createProject/step2/examDialog.vue";
import { inject, type Ref, ref, watch } from "vue";
import { examType, examTypeDict } from "@/utils/constant";
import { getTextSection, type params4submitTextSection, submitTextSection } from "@/apis/path/createProject";
import { ElMessage } from "element-plus";
import type { examSectionType } from "@/utils/type";
import { findKeyByValue } from "@/utils/func";
import {
  processAllLatexEquations,
  convertImgTagsToMarkdown,
} from "@/utils/latexUtils";

// 本组件进行前后端通信
const emits = defineEmits(['sendSecName', 'saveSectionDraftDone', 'saveSectionDone']);
const props = defineProps({
  projectId: Number,
  sectionId: Number,
  sectionCount: Number,
});
const prjId = ref();
const secId = ref();
const secCount = ref();
const secName = ref();
const dialogRef = ref();
const examList = ref<examSectionType[]>([]);
const initData = () => {
  if (secId.value) {
    getTextSection(secId.value).then(res => {
      if (res.success) {
        const textSection = res.data.wordsContent.projectSections[0];
        secName.value = textSection.sectionTitle;
        emits('sendSecName', secName.value);
        examList.value = textSection.sectionContent?.map((e: examSectionType) => {
          return {
            ...e,
            examType: examTypeDict[e.examType],
          }
        });
      }
      else {
        ElMessage.error(res.message);
      }
    }).catch()
  }
}
watch(() => props, (newValue, oldValue) => {
  prjId.value = newValue.projectId;
  secId.value = newValue.sectionId;
  secCount.value = newValue.sectionCount
  if (secId.value == -1) {
    return;
  }
  initData();
}, { deep: true, immediate: true });

const setSectionName = (newSecName: string) => {
  secName.value = newSecName;
}
const handleAddExam = (type: examType) => {
  dialogRef.value.showDialog(type);
}
const handleEditExam = (data: examSectionType, idx: number) => {
  dialogRef.value.showDialog(data.examType, data, idx);
}
const handleDialogSubmit = (newExam: examSectionType, editIndex?: number) => {
  if (editIndex !== undefined) {
    examList.value[editIndex] = newExam;
  }
  else {
    examList.value?.push(newExam);
  }
}
const handleDelete = (index: number) => {
  examList.value.splice(index, 1);
}
const handleSavaDraftSection = () => {
  const param: params4submitTextSection = {
    projectId: prjId.value,
    projectSections: [{
      sectionId: secId.value,
      sectionNum: secCount.value,
      sectionTitle: secName.value,
      prText: '',
      sectionContent: examList.value,
      htmlContent: '',
    }],
  }
  submitTextSection(param).then(res => {
    if (res.success) {
      // 表明本次保存草稿结束，重置父组件中的saveSectionId
      // ElMessage.success(res.message);
      emits('saveSectionDraftDone', true);
    }
    else {
      ElMessage.error(res.message);
      emits('saveSectionDraftDone', false);
    }
  }).catch()
}
const handleSavaSection = () => {
  if (examList.value.length == 0) {
    ElMessage.error('请添加题目');
    emits('saveSectionDone', false);
    return;
  }
  const param: params4submitTextSection = {
    projectId: prjId.value,
    projectSections: [{
      sectionId: secId.value,
      sectionNum: secCount.value,
      sectionTitle: secName.value,
      prText: '',
      sectionContent: examList.value,
      htmlContent: '',
    }],
  }
  console.log('going to save draft examSection: ' + JSON.stringify(param, null, 2));
  submitTextSection(param).then(res => {
    if (res.success) {
      // 表明本次保存草稿结束，重置父组件中的saveSectionId
      // ElMessage.success(res.message);
      emits('saveSectionDone', true);
    }
    else {
      ElMessage.error(res.message);
      emits('saveSectionDone', false);
    }
  }).catch()
}
const saveDraftSectionId = inject('saveDraftSection') as Ref;
const saveSectionId = inject('saveSection') as Ref;
watch(() => saveDraftSectionId, (newVal, oldVal) => {
  if (newVal.value != -1 && secId.value == newVal.value) {
    console.log('im gonna save draft！' + secId.value);
    handleSavaDraftSection();
  }
}, { deep: true, immediate: true });
watch(() => saveSectionId, (newVal, oldVal) => {
  if (newVal.value != -1 && secId.value == newVal.value) {
    console.log('im gonna save！' + secId.value);
    handleSavaSection();
  }
}, { deep: true, immediate: true });
defineExpose({
  setSectionName,
})
</script>

<template>
  <div class="exam-content-wrapper">
    <div class="btn-group">
      <my-button @click="handleAddExam(examType.blank)" type="group" :height="38">+ 填空题</my-button>
      <my-button @click="handleAddExam(examType.select)" type="group" :height="38">+ 选择题</my-button>
      <my-button @click="handleAddExam(examType.judge)" type="group" :height="38">+ 判断题</my-button>
      <my-button @click="handleAddExam(examType.qa)" type="group" :height="38">+ 问答题</my-button>
    </div>
    <div class="exam-container">
      <exam-line v-for="(e, idx) in examList" :key="idx">
        <template v-slot:index>
          {{ idx + 1 }}
        </template>
        <template v-slot:type>
          {{ findKeyByValue(e.examType, examTypeDict) }}
        </template>
        <template v-slot:content>
          <span class="exam-title-container" @click="handleEditExam(e, idx)">
            {{ e.examTitle }}
          </span>
        </template>
        <template v-slot:footer>
          <span class="btn" @click="handleDelete(idx)">
            ×
          </span>
        </template>
      </exam-line>
    </div>
  </div>
  <exam-dialog ref="dialogRef" @submit="handleDialogSubmit"></exam-dialog>
</template>

<style scoped>
.exam-content-wrapper {
  margin-bottom: 20px;
  width: 100%;

  .btn-group {
    display: flex;
    flex-direction: row;
  }

  .exam-container {
    .exam-title-container {
      height: 100%;
      display: flex;
      width: 100%;
      align-items: center;
      padding: 10px;

      &:hover {
        cursor: pointer;
        background-color: var(--color-light);
      }
    }

    .btn {
      cursor: pointer;
    }
  }
}</style>