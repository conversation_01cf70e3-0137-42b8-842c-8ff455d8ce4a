<script setup lang="ts">
import myTag from "@/views/common/myTag.vue";
import MySteps from "@/views/createProject/components/mySteps.vue";
import MyButton from "@/views/common/myButton.vue";
import PrjInfo from "@/views/common/prjInfo.vue";
import VideoContent from "@/views/createProject/step2/videoContent.vue";
import addTDialog from "./addTDialog.vue";
import sectionContainer from "@/views/createProject/step2/sectionContainer.vue";
import AddNewSection from "@/views/createProject/step2/addNewSection.vue";
import TextContent from "@/views/createProject/step2/textContent.vue";
import TitleForm from "@/views/createProject/components/titleForm.vue";

import {
  onBeforeMount,
  onMounted,
  provide,
  reactive,
  ref,
  nextTick,
  onBeforeUnmount,
  type Ref,
  onUpdated,
} from "vue";
import { editingPrjStore } from "@/stores/editingPrj";
import {
  deleteTextSection,
  deleteVideoSection,
  getProjectDetail,
  type params4saveDraft,
} from "@/apis/path/createProject";
import { formatData_step1, findKeyByValue } from "@/utils/func";
import type {
  prjInfoType,
  tagType,
  simpleSectionInfo,
  taskType,
} from "@/utils/type";
import { ElMessage, ElMessageBox, type FormInstance } from "element-plus";
import { useRoute, useRouter } from "vue-router";
import { submitProject } from "@/apis/path/createProject";
import { prjForm, prjType, typeDict } from "@/utils/constant";
import { saveDraftAPI } from "@/apis/path/createProject";

const formRef = ref<FormInstance>();
const route = useRoute();
const router = useRouter();
const editingPrj = editingPrjStore();
const curPrjForm = ref();
const prjId = ref();
const prjRlsDate = ref();
const curSectionId = ref();
const curSectionCount = ref(); // 只为单节视频提供
const curSectionName = ref();
const wait2ShowSecId = ref(-1);
const saveDraftSectionId = ref();
const saveSectionId = ref();
const taskList = ref<taskType[]>([]);
const addNewFlag = ref<Boolean>(false);
const submitPrjFlag = ref<Boolean>(false);
const foldSecFlag = ref<Boolean>(false);
const preStepFlag = ref<Boolean>(false);
const dialogRef = ref();
// 提交项目：0 ; 增加小节：1 ; 折叠小节：2
const handleFlag = ref<Number>(-1);
const needOpenFlag = ref(false);
// 重置flag
const resetHandleFlag = () => {
  handleFlag.value = -1;
};
provide("taskList", taskList as Ref<taskType[]>);
// 把响应式数据provide出去,用于告知子组件保存
provide("saveDraftSection", saveDraftSectionId);
provide("saveSection", saveSectionId);
const prjInfoData = ref<prjInfoType>({
  disable: true,
  prjType: prjType.exam,
  prjName: "",
  prjAim: "",
  prjGeneral: "",
  prjTagList: [] as tagType[],
  prjTargetList: [] as tagType[],
  prjAreaList: [] as tagType[],
  prjCover: {
    commUrl: "",
    echoUrl: "",
  },
});
const secList = ref<simpleSectionInfo[]>([
  {
    sectionId: 1,
    sectionName: "string",
    sectionNum: 1,
  },
]);

// 处理小节折叠
const handleFold = () => {
  if (!foldSecFlag.value) {
    handleFlag.value = 2;
    handleToValidContent();
  } else {
    if (curSectionId.value == -1) {
      console.log("目前应该是全部收起的状态，但是还是触发了收起事件");
    } else {
      // saveSectionId.value = curSectionId.value;
      curSectionId.value = -1;
      foldSecFlag.value = false;
    }
  }

  // saveSection作为响应式变量provide出去，子组件里应该能监听到这个变化
  // 但是saveSection需要重置，要不然重复保存同一小节的时候就会失效
  // 重置时机怎么搞？子组件emit发信，本组件接收一个重置事件（表明save完成）
};
let tempOpenId;
const handleOpen = (openId: number) => {
  if (curSectionId.value != -1) {
    wait2ShowSecId.value = openId;
    //   有小节正处于打开状态，先保存
    handleFold();
    // curSectionId.value = openId
    tempOpenId = openId;
    needOpenFlag.value = true;
  } else {
    curSectionId.value = openId;
  }
};
const handleSaveDraftSectionDone = (success: boolean) => {
  saveDraftSectionId.value = -1;
  if (success) {
    // curSectionId.value = wait2ShowSecId.value
    wait2ShowSecId.value = -1;
  }
  console.log("保存草稿结束，清空saveSecId");
};
const handleAddArea = () => {
  dialogRef.value.showDialog("area", 1, 1);
};
const handleDeleteArea = (aimId: string) => {
  // 维护editingPrj_AreaList
  editingPrj.removeArea(aimId);
  prjInfoData.value.prjAreaList = [...editingPrj.getPrjAreaList()];
};
const handleAddTarget = () => {
  dialogRef.value.showDialog("klg", 1);
};
const handleDeleteTarget = (aimId: string) => {
  // 维护editingPrj_TargetList
  editingPrj.removeTarget(aimId);
  prjInfoData.value.prjTargetList = [...editingPrj.getPrjTargetList()];
};
const handleSaveSecDone = (success: boolean) => {
  // 当且仅当点击提交时，对应的content组件验证、传后端，然后发信，被本函数接收，然后直接跳转
  saveSectionId.value = -1;
  if (success) {
    if (handleFlag.value === 0) {
      submitPrjFlag.value = true;
      handleNextStep();
      resetHandleFlag();
    } else if (handleFlag.value === 1) {
      addNewFlag.value = true;
      resetHandleFlag();
      // 后续操作已经确保addNewFlag更新为false
    } else if (handleFlag.value === 2) {
      foldSecFlag.value = true;
      handleFold();
      if (needOpenFlag.value) {
        handleOpen(tempOpenId);
        needOpenFlag.value = false;
      }
      resetHandleFlag();
    } else if (handleFlag.value === 3) {
      preStepFlag.value = true;
      handlePreStep();
      resetHandleFlag();
    }
  } else {
  }
};
const handleDelete = (deleteId: number) => {
  if (secList.value.length === 1) {
    ElMessage.info("至少要有一个小节！");
  } else {
    if (curPrjForm.value == prjForm.video) {
      deleteVideoSection(deleteId)
        .then((res) => {
          if (res.success) {
            ElMessage.success("删除成功");
            const index = secList.value.findIndex(
              (s) => s.sectionId == deleteId
            );
            secList.value.splice(index, 1);
            if (curSectionId.value === deleteId) {
              curSectionId.value = -1;
            }
          } else {
            ElMessage.error("删除失败");
          }
        })
        .catch();
    } else if (curPrjForm.value == prjForm.text) {
      deleteTextSection(deleteId)
        .then((res) => {
          if (res.success) {
            ElMessage.success("删除成功");
            const index = secList.value.findIndex(
              (s) => s.sectionId == deleteId
            );
            secList.value.splice(index, 1);
            if (curSectionId.value === deleteId) {
              curSectionId.value = -1;
            }
          } else {
            ElMessage.error("删除失败");
          }
        })
        .catch();
    } else {
      console.log("类型错误，删除失败");
    }
  }
};
// 处理添加新节
const handleNewSection = (newId: number) => {
  console.log("add new section");
  curSectionId.value = newId;
  curSectionCount.value = secList.value.length;
  secList.value.push({
    sectionId: curSectionId.value,
    sectionName: "",
    sectionNum: curSectionCount.value,
  });
  ElMessage.success("添加小节成功");
  addNewFlag.value = false;
};
const handlePreStep = () => {
  // step1的初始化用到的id直接路由参数传过去
  ElMessageBox.confirm("如果没有保存新增内容会丢失", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      router.push({
        path: "/home/<USER>/step1",
        query: {
          prjId: prjId.value,
          prjForm: curPrjForm.value,
        },
      });
    })
    .catch(() => {});
};
const handleSaveDraft = () => {
  const param: params4saveDraft = {
    oid: prjId.value,
    targetKlg: [],
    prjType: findKeyByValue(prjInfoData.value.prjType, typeDict),
    status: 0,
  };
  switch (prjInfoData.value.prjType) {
    case "4":
      param.targetKlg = prjInfoData.value.prjAreaList?.map(
        (i: tagType) => i.id
      );
      break;
    default:
      param.targetKlg = prjInfoData.value.prjTargetList?.map(
        (i: tagType) => i.id
      );
      break;
  }
  saveDraftAPI(param)
    .then((res) => {
      if (res.success) {
        // ElMessage.success("保存成功");
        return true;
      } else {
        ElMessage.error(res.message);
      }
    })
    .catch();
  if (curSectionId.value != -1) {
    // 取消注释后，点‘存草稿’不会收起展开的小节
    // wait2ShowSecId.value = curSectionId.value;
    saveDraftSectionId.value = curSectionId.value;
  }
  // else {
  //   saveSectionId.value = curSectionId.value;
  // }
};

const handleNextStep = () => {
  if (prjInfoData.value.prjType !== prjType.prj) {
    formRef.value?.validate((valid) => {
      if (valid) {
        // submitPrjFlag.value = true;
        if (submitPrjFlag.value || curSectionId.value === -1) {
          for (let i = 0; i < taskList.value.length; i++) {
            if (taskList.value[i].taskProcess < 100) {
              ElMessage.error("有小节视频未完成上传");
              return;
            }
          }
          ElMessageBox.confirm(
            "提交后项目会进入审核状态，确定提交吗?",
            "提示",
            {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }
          )
            .then(() => {
              const param: params4saveDraft = {
                oid: prjId.value,
                targetKlg: [],
                prjType: findKeyByValue(prjInfoData.value.prjType, typeDict),
                status: 0,
              };
              switch (prjInfoData.value.prjType) {
                case "4":
                  param.targetKlg = prjInfoData.value.prjAreaList?.map(
                    (i: tagType) => i.id
                  );
                  break;
                default:
                  param.targetKlg = prjInfoData.value.prjTargetList?.map(
                    (i: tagType) => i.id
                  );
                  break;
              }
              saveDraftAPI(param)
                .then((res) => {
                  if (res.success) {
                    // ElMessage.success("保存成功");
                    submitProject(prjId.value).then((res) => {
                      if (res.success) {
                        submitPrjFlag.value = false;
                        window.close();
                      } else {
                        ElMessage.error(res.message);
                      }
                    });
                    return true;
                  } else {
                    ElMessage.error(res.message);
                  }
                })
                .catch();
            })
            .catch(() => {});
        } else {
          handleFlag.value = 0;
          handleToValidContent();
        }
      } else {
        // ElMessage.warning("验证不通过")
      }
    });
  } else {
    if (submitPrjFlag.value || curSectionId.value === -1) {
      for (let i = 0; i < taskList.value.length; i++) {
        if (taskList.value[i].taskProcess < 100) {
          ElMessage.error("有小节视频未完成上传");
          return;
        }
      }
      ElMessageBox.confirm("提交后项目会进入审核状态，确定提交吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const param: params4saveDraft = {
            oid: prjId.value,
            targetKlg: [],
            prjType: findKeyByValue(prjInfoData.value.prjType, typeDict),
            status: 0,
          };
          switch (prjInfoData.value.prjType) {
            case "4":
              param.targetKlg = prjInfoData.value.prjAreaList?.map(
                (i: tagType) => i.id
              );
              break;
            default:
              param.targetKlg = prjInfoData.value.prjTargetList?.map(
                (i: tagType) => i.id
              );
              break;
          }
          saveDraftAPI(param)
            .then((res) => {
              if (res.success) {
                // ElMessage.success("保存成功");
                submitProject(prjId.value).then((res) => {
                  if (res.success) {
                    submitPrjFlag.value = false;
                    window.close();
                  } else {
                    ElMessage.error(res.message);
                  }
                });
                return true;
              } else {
                ElMessage.error(res.message);
              }
            })
            .catch();
        })
        .catch(() => {});
    } else {
      handleFlag.value = 0;
      handleToValidContent();
    }
  }
};

const handleToValidContent = (value?: number) => {
  if (value) {
    handleFlag.value = value;
  }
  saveSectionId.value = curSectionId.value;
};

const refreshTList = (tList: tagType[], tForm: string) => {
  if (tForm == "tag") {
    // editingPrj.setPrjTagList(tList);
    // formData.prjTagList = [];
    // formData.prjTagList.push(...editingPrj.getPrjTagList());
  } else if (tForm == "klg") {
    editingPrj.setPrjTargetList(tList);
    prjInfoData.value.prjTargetList = [];
    prjInfoData.value.prjTargetList.push(...editingPrj.getPrjTargetList());
  } else if (tForm == "area") {
    editingPrj.setPrjAreaList(tList);
    prjInfoData.value.prjAreaList = [];
    prjInfoData.value.prjAreaList.push(...editingPrj.getPrjAreaList());
  }
};

const getProject = () => {
  getProjectDetail(prjId.value).then((res: any) => {
    if (res.success) {
      let baseData = res.data.list[0];
      // 这个部分后端传来的和接受的都是typeName，前端存储都转换成了typeCode
      // 因为整个后端没有统一传输到底是code还是name，所以前端自己统一一下数据便于管理
      // 在formatData_step1中，把type字段转化成typeName
      prjInfoData.value = formatData_step1(baseData);
      prjRlsDate.value = baseData.createTime;
      secList.value = res.data.sectionList?.map((sec: any) => {
        return {
          sectionId: sec.sectionId,
          sectionName: sec.sectionTitle,
          sectionNum: sec.sectionNum,
        };
      });
      curSectionId.value = secList.value[0].sectionId;
      curSectionCount.value = secList.value[0].sectionNum;
      curSectionName.value = secList.value[0].sectionName;
    } else {
      ElMessage.error("Step2项目信息获取失败");
      router.push(`/home/<USER>
    }
  });
};
onBeforeMount(() => {
  prjId.value = parseInt(route.query.prjId as string);
  curPrjForm.value = editingPrj.getForm();
});

onMounted(() => {
  getProject();
});
</script>

<template>
  <my-steps :action="1" :form="curPrjForm"></my-steps>
  <div class="main-container">
    <title-form :form="curPrjForm"></title-form>
    <div class="line"></div>
    <div class="content-wrapper">
      <prj-info :info-data="prjInfoData"></prj-info>
      <!-- TODO：有空改下结构，现在的结构好蠢 -->

      <div style="width: 100%">
        <div style="margin-bottom: 40px; width: 100%">
          <div class="additional_block" v-show="prjInfoData.prjType !== '2'">
            <el-form :model="prjInfoData" ref="formRef">
              <el-form-item
                v-if="prjInfoData.prjType == '1' || prjInfoData.prjType == '3'"
                required
                :label="(prjInfoData.prjType == '1' ? '讲解' : '测评') + '目标'"
                prop="prjTargetList"
                :rules="[{ required: true, message: '请选择目标' }]"
              >
                <div>
                  <my-button @click="handleAddTarget" style="margin-left: 40px"
                    >+ 添加知识</my-button
                  >
                  <div class="selectedWrapper">
                    <my-tag
                      class="t"
                      v-for="klg in prjInfoData.prjTargetList"
                      :tag-id="klg.id"
                      :key="klg.id"
                      @delete="handleDeleteTarget(klg.id)"
                      type="target"
                    >
                      <el-tooltip
                        popper-class="tooltip-width"
                        :content="klg.name"
                        raw-content
                      >
                        <span
                          class="htmlContent3 ck-content"
                          v-html="klg.name"
                        ></span>
                      </el-tooltip>
                    </my-tag>
                  </div>
                </div>
              </el-form-item>
              <el-form-item
                label="讲解目标"
                required
                prop="prjAreaList"
                v-if="prjInfoData.prjType == '4'"
                :rules="[{ required: true, message: '请选择目标' }]"
              >
                <my-button @click="handleAddArea" style="margin-left: 40px"
                  >+ 添加领域</my-button
                >
                <div class="selectedWrapper">
                  <my-tag
                    class="t"
                    v-for="klg in prjInfoData.prjAreaList"
                    :tag-id="klg.id"
                    :key="klg.id"
                    @delete="handleDeleteArea(klg.id)"
                    type="target"
                  >
                    <el-tooltip
                      popper-class="tooltip-width"
                      :content="klg.name"
                      raw-content
                    >
                      <span
                        class="htmlContent3 ck-content"
                        v-html="klg.name"
                      ></span>
                    </el-tooltip>
                  </my-tag>
                </div>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
      <!-- 视频 -->
      <template v-if="curPrjForm == prjForm.video">
        <template v-if="prjInfoData.prjType == prjType.prj">
          <!-- 案例 -->
          <div class="section-wrapper">
            <el-collapse
              v-model="curSectionId"
              style="margin-bottom: 40px"
              accordion
            >
              <!-- name字段是手风琴的唯一标识 -->
              <el-collapse-item
                v-for="(sec, index) in secList"
                :name="sec.sectionId"
                :key="sec.sectionId"
              >
                <template #title>
                  <div class="collapse-title" @click.stop>
                    第{{ index + 1 }}节
                    <span class="btns">
                      <template v-if="curSectionId == sec.sectionId">
                        <span class="b" @click="handleFold"> 收起 </span>
                        <span class="b" @click="handleDelete(sec.sectionId)">
                          删除
                        </span>
                      </template>
                      <template v-else>
                        <span class="b" @click="handleOpen(sec.sectionId)">
                          展开
                        </span>
                        <span class="b" @click="handleDelete(sec.sectionId)">
                          删除
                        </span>
                      </template>
                    </span>
                  </div>
                </template>
                <section-container
                  @saveSectionDraftDone="handleSaveDraftSectionDone"
                  @saveSectionDone="handleSaveSecDone"
                  :project-form="prjForm.video"
                  :project-type="prjInfoData.prjType"
                  :projectId="prjId"
                  :showSecId="curSectionId"
                  :sectionId="sec.sectionId"
                  :sectionCount="sec.sectionNum"
                  :sectionName="sec.sectionName"
                  :releaseDate="prjRlsDate"
                >
                </section-container>
              </el-collapse-item>
            </el-collapse>
            <add-new-section
              :form="(curPrjForm as number)"
              :projectId="prjId"
              :listLength="secList.length"
              :addNewFlag="addNewFlag"
              :curSecId="curSectionId"
              @sendToValidContent="handleToValidContent"
              @sendNewSection="handleNewSection"
            >
            </add-new-section>
          </div>
        </template>
        <template v-else>
          <video-content
            @saveSectionDraftDone="handleSaveDraftSectionDone"
            @saveSectionDone="handleSaveSecDone"
            :projectId="prjId"
            :sectionId="curSectionId"
            :sectionCount="curSectionCount"
            :releaseDate="prjRlsDate"
          ></video-content>
        </template>
      </template>

      <!-- 文稿 -->
      <template v-else-if="curPrjForm == prjForm.text">
        <!-- 知识讲解（单节） -->
        <template v-if="prjInfoData.prjType == prjType.klg">
          <text-content
            @saveSectionDraftDone="handleSaveDraftSectionDone"
            @saveSectionDone="handleSaveSecDone"
            :projectId="prjId"
            :sectionId="curSectionId"
            :sectionCount="curSectionCount"
            :releaseDate="prjRlsDate"
          ></text-content>
        </template>
        <!-- 文稿多节 -->
        <template v-else>
          <div class="section-wrapper">
            <el-collapse
              v-model="curSectionId"
              style="margin-bottom: 40px"
              accordion
            >
              <!-- name字段是手风琴的唯一标识 -->
              <el-collapse-item
                v-for="(sec, index) in secList"
                :name="sec.sectionId"
                :key="sec.sectionId"
              >
                <template #title>
                  <div class="collapse-title" @click.stop>
                    第{{ index + 1 }}节
                    <span class="btns">
                      <template v-if="curSectionId == sec.sectionId">
                        <span class="b" @click="handleFold"> 收起 </span>
                        <span class="b" @click="handleDelete(sec.sectionId)">
                          删除
                        </span>
                      </template>
                      <template v-else>
                        <span class="b" @click="handleOpen(sec.sectionId)">
                          展开
                        </span>
                        <span class="b" @click="handleDelete(sec.sectionId)">
                          删除
                        </span>
                      </template>
                    </span>
                  </div>
                </template>
                <section-container
                  @saveSectionDraftDone="handleSaveDraftSectionDone"
                  @saveSectionDone="handleSaveSecDone"
                  :project-form="prjForm.text"
                  :project-type="prjInfoData.prjType"
                  :projectId="prjId"
                  :showSecId="curSectionId"
                  :sectionId="sec.sectionId"
                  :sectionCount="sec.sectionNum"
                  :sectionName="sec.sectionName"
                  :releaseDate="prjRlsDate"
                >
                </section-container>
              </el-collapse-item>
            </el-collapse>
            <add-new-section
              :form="(curPrjForm as number)"
              :projectId="prjId"
              :listLength="secList.length"
              :addNewFlag="addNewFlag"
              :curSecId="curSectionId"
              @sendToValidContent="handleToValidContent"
              @sendNewSection="handleNewSection"
            >
            </add-new-section>
          </div>
        </template>
      </template>
      <div class="tool-bar">
        <my-button type="light" @click="handlePreStep()">上一步</my-button>
        <my-button :clickable="curSectionId != -1" @click="handleSaveDraft()"
          >存草稿</my-button
        >
        <my-button @click="handleNextStep()">提交</my-button>
      </div>
    </div>
  </div>
  <!-- OTHER -->
  <add-t-dialog ref="dialogRef" @submit="refreshTList"></add-t-dialog>
</template>

<style scoped>
.main-container {
  font-family: var(--font-family-text);
  background-color: white;
  color: var(--color-black);
  width: var(--width-content);
  /* margin: 20px auto auto auto; */
  display: flex;
  flex-direction: column;
  align-items: center;
  .additional_block {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 10px;
    background-color: var(--color-light);
    .title {
      font-size: 16px;
      font-weight: 600;
    }
    .selectedWrapper {
      padding-right: 20px;
      display: flex;
      width: 100%;
      align-items: flex-start;
      flex-direction: row;
      margin-left: 40px;
      flex-wrap: wrap;
      margin-top: 10px;
      .t {
        margin-right: 30px;
        margin-bottom: 10px;
      }
    }
  }

  .content-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    padding: 30px;

    :deep(.el-form-item__label) {
      padding: 0;
    }

    .tool-bar {
      width: 420px;
      display: flex;
      justify-content: space-between;
    }

    .section-wrapper {
      width: 100%;

      :deep(.el-collapse-item__header) {
        height: 100%;
        margin-bottom: 10px;
      }

      .collapse-title {
        display: flex;
        width: 100%;
        justify-content: space-between;
        height: 35px;
        line-height: 35px;
        background-color: var(--color-primary);
        color: white;
        padding: 0 10px;
        cursor: default;

        .btns {
          display: flex;
          width: 88px;
          justify-content: space-between;

          .b {
            cursor: pointer;
          }
        }
      }

      :deep(.el-collapse-item__arrow) {
        display: none;
      }
    }
  }
}
</style>
