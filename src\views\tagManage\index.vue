<script setup lang="ts">

import FormSwitch from "@/views/common/formSwitch.vue";
import MyFlipper from "@/views/common/myFlipper.vue";
import {ElMessage} from "element-plus";
import MyButton from "@/views/common/myButton.vue";
import {onMounted, ref} from "vue";
import {getTagList, type key2TagList, type params2tag, addTag, editTag, deleteTag} from "@/apis/path/tagManage";
import TagDialog from "@/views/tagManage/components/tagDialog.vue";

const prjTable = ref();
const tableData = ref([]);
const searchKey = ref('');
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tagDialogRef = ref();
interface tagType {
  createTime: string,
  isOk: number,
  count: number,
  orderNum: number,
  id: number,
  title: string,
}
const getTgsList = () => {
  const param = ref<key2TagList>({
    current: currentPage.value,
    limit: pageSize.value,
    keywords: searchKey.value,
  });
  getTagList(param.value).then((res) => {
    if(res.success) {
      tableData.value = res.data.list;
      total.value = res.data.total;
    }
    else {
      ElMessage.error('加载列表失败');
    }
  }).catch();
}
const handleSearch = (key: string) => {
  searchKey.value = key;
  getTgsList();
}
const handleNewTag = () => {
  tagDialogRef.value.showDialog();
}
const handleEdit = (id: number, name: string, order: number) => {
  tagDialogRef.value.showDialog(id, name, order);
}
const handleDelete = (id: number) => {
  deleteTag(id).then(res => {
    if(res.success) {
      ElMessage.success('删除成功');
      getTgsList();
    }
    else {
      ElMessage.error('删除失败');
    }
  })
}
const handleChangeTagStatus = (id: number, newVal: number) => {
  const param = ref<params2tag>({
    oid: id,
    isValid: newVal,
  });
  editTag(param.value).then(res => {
    if(res.success) {
      ElMessage.success('修改成功');
    }
    else {
      ElMessage.error('修改失败');
    }
  }).catch();
}
const handleChangePage = (newPage: number) => {
  currentPage.value = newPage;
  getTgsList();
}
const handleAddTag = (param: params2tag) => {
  addTag(param).then((res) => {
    if(res.success) {
      ElMessage.success('添加成功');
      getTgsList();
    }
    else {
      ElMessage.error('添加失败');
    }
  })
}
const handleEditTag = (param: params2tag) => {
  editTag(param).then((res) => {
    if(res.success) {
      ElMessage.success('修改成功');
      getTgsList();
    }
    else {
      ElMessage.error('修改失败');
    }
  })
}
const indexMethod = (index: number) => {
  return (currentPage.value - 1) * pageSize.value + index + 1;
}
onMounted(() => {
  getTgsList();
})
</script>

<template>
  <div class="main-container">
    <form-switch :form-func="false" @search="handleSearch" placeholder="请输入项目标题"></form-switch>
    <div class="content-container">
      <div class="toolbar">
        <my-button type="primary" @click="handleNewTag()">新增标签</my-button>
      </div>
      <div class="line"></div>
      <el-table ref="prjTable" :data="tableData" style="width: 100%" empty-text="暂无数据">
        <el-table-column type="index" :index="indexMethod" label="序号" width="80" >
        </el-table-column>
        <el-table-column prop="title" label="标签名称" width="332" >
          <template #default="scope">
            <span style="width: 100%; justify-content: flex-start">
              <span class="ck-content" v-html="scope.row.title"></span>
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="count" label="被引用数量" width="153" >
        </el-table-column>
        <el-table-column prop="createTime" label="发布日期" width="195" ></el-table-column>
        <el-table-column prop="isOk" label="是否启用" width="190">
          <template #default="scope">
            <el-switch @change="handleChangeTagStatus(scope.row.id, scope.row.isOk)" v-model="scope.row.isOk" :active-value="1" :inactive-value="0"/>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="190">
          <template #default="scope">
            <span class="operationBlock">
              <span @click="handleEdit(scope.row.id, scope.row.title, scope.row.orderNum)" class="operationBtn">编辑</span>
              <span @click="handleDelete(scope.row.id)" class="operationBtn">删除</span>
            </span>
          </template>
        </el-table-column>
      </el-table>
      <my-flipper @change-page="handleChangePage" :current="currentPage" :page-size="pageSize" :total="total"></my-flipper>
    </div>
  </div>
  <tag-dialog ref="tagDialogRef" @submit-add="handleAddTag" @submit-edit="handleEditTag"></tag-dialog>
</template>

<style scoped>
.main-container {
  font-family: var(--font-family-text);
  color: var(--color-black);
  width: var(--width-content);
  /* margin: 20px auto auto auto; */
  .content-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 20px 30px 30px 30px;
    background-color: white;
    .toolbar {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-direction: row;
      padding-bottom: 20px;
      /* &::after {
        content: '';
        height: 1px;
        width: 100%;
        background-color: var(--color-line);
        position: absolute;
        bottom: 0;
      } */
    }
    .el-table {
      &:deep(.el-table__cell) {
        padding: 0;
      }
      &:deep(.cell) {
        justify-content: center;
        display: flex;
        align-items: center;
        min-height: 46px;
      }
    }
    .operationBlock {
      display: flex;
      width: 190px;
      justify-content: space-between;
      align-items: center;
      flex-direction: row;
      .operationBtn {
        color: var(--color-primary);
        &:hover {
          cursor: pointer;
          color: var(--color-primary);
          font-weight: 600;
            /* text-decoration: underline; */
        }
      }
    }
  }
}

</style>