import { http } from '@/apis';
import type {APIResponse} from "@/utils/type";
export interface key2TagList {
    current: number,
    limit: number,
    keywords?: string,
    // status: number,
}

export function getTagList (param: key2TagList): Promise<APIResponse> {
    return http.request({
        method: 'post',
        url: '/tag/getList',
        data: param
    });
}
export interface params2tag {
    oid: number,
    orderNum?: number,
    isValid?: number,
    tagName?: string
}
export function addTag (param: params2tag): Promise<APIResponse> {
    return http.request({
        method: 'post',
        url: '/tag/save',
        data: param
    });
}
export function editTag (param: params2tag): Promise<APIResponse> {
    return http.request({
        method: 'post',
        url: '/tag/edit',
        data: param
    });
}
export function deleteTag (id: number): Promise<APIResponse> {
    return http.request({
        method: 'get',
        url: `/tag/remove/${id}`,
    });
}
