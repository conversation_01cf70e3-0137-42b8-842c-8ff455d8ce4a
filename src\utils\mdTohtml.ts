/**
 * 将markdown文本中的数学公式转换为script标签格式，然后使用markdown-it进行渲染
 *
 * 处理流程：
 * 1. 先匹配块级公式 $$公式$$ 转换为 <script type="math/tex; mode=display">公式</script>
 * 2. 再匹配行内公式 $公式$ 转换为 <script type="math/tex">公式</script>
 * 3. 使用markdown-it进行最终渲染
 */
/**
 * 简化版本的markdown渲染函数，仅处理数学公式转换
 * 不依赖markdown-it，适用于只需要公式转换的场景
 *
 * @param text - 包含数学公式的文本
 * @returns 转换后的文本
 */
import MarkdownIt from "markdown-it";

function convertMathFormulas(text: string): string {
  // 优化：处理块级公式 $$公式$$
  // \s* 匹配零个或多个空白字符
  // 将 \s* 放在捕获组 (.*?) 的外部，这样匹配到的空格就不会被包含在公式内
  let result = text.replace(/\$\$\s*(.*?)\s*\$\$/gs, (_, formula) => {
    return `<script type="math/tex; mode=display">${formula}</script>`;
  });

  // 优化：处理行内公式 $公式$
  // 同样应用 \s* 技巧
  // 注意：原正则表达式中的 (?:[^$\n]|\\\$)+? 部分是为了正确处理公式中包含 \$ 的情况，我们需要保留它。
  result = result.replace(
    /(?<!\$)\$(?!\$)\s*((?:[^$\n]|\\\$)+?)\s*\$(?!\$)/g,
    (_, formula) => {
      return `<script type="math/tex">${formula}</script>`;
    }
  );

  return result;
}

/**
 * 渲染md为html
 */
export function markdownToHtml(markdownText: string): string {
  // 先转换数学公式
  const processedText = convertMathFormulas(markdownText);

  // 如果提供了MarkdownIt构造函数，则使用它进行渲染
  const md = new MarkdownIt({
    html: true, // 允许HTML标签
    breaks: true, // 将换行符转换为<br>
    linkify: true, // 自动识别链接
  });

  return md.render(processedText);
}
// 转义html

// export function encodeHTML(str: string) {
//   // 提取所有img标签，包括自闭合和非自闭合形式
//   const imgTags: string[] = [];
//   const imgRegex = /<img[^>]*\/?>/gi;

//   // 用占位符替换img标签，并保存原始标签
//   const processedStr = str.replace(imgRegex, (match) => {
//     const index = imgTags.length;
//     imgTags.push(match);
//     return `__IMG_PLACEHOLDER_${index}__`;
//   });

//   // 创建一个临时的div元素
//   const tempDiv = document.createElement("div");

//   // 将处理后的字符串设置为div的文本内容，此时< >等字符被当作纯文本
//   tempDiv.textContent = processedStr;

//   // 读取div的innerHTML，浏览器会自动返回编码后的HTML字符串
//   let encodedHTML = tempDiv.innerHTML;

//   // 将占位符替换回原始的img标签
//   imgTags.forEach((imgTag, index) => {
//     encodedHTML = encodedHTML.replace(`__IMG_PLACEHOLDER_${index}__`, imgTag);
//   });

//   return encodedHTML;
// }
export function encodeHTML(str: string) {
  // 创建一个临时的div元素
  console.log("str",str)
  const tempDiv = document.createElement("div");

  // 将处理后的字符串设置为div的文本内容，此时< >等字符被当作纯文本
  tempDiv.textContent = str;

  // 读取div的innerHTML，浏览器会自动返回编码后的HTML字符串
  const encodedHTML = tempDiv.innerHTML;
  console.log("en:",encodedHTML)
  return encodedHTML;
}
