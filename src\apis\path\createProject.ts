import { http } from "@/apis";
import type { APIResponse, textSection } from "@/utils/type";

export function createProject(prForm: number): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: "/tprj/initProject",
    data: {
      prForm: prForm,
    },
  });
}
export function getProjectDetail(prjId: number): Promise<APIResponse> {
  return http.request({
    method: "get",
    url: `/tprj/detail`,
    params: {
      projectId: prjId,
    },
  });
}
export function uploadImage(param: FormData): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: "/cos/file/imageCosUpload",
    data: param,
  });
}
export function uploadImage4CkEditor(param: FormData): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: "/cos/file/imageCosUploadF",
    data: param,
  });
}
export interface params2tList {
  current: number;
  limit: number;
  keywords: string;
  status: number;
}
export function getTagList(param: params2tList): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: `/tag/getList`,
    data: param,
  });
}
export function getTargetList(param: params2tList): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: `/klg/getList`,
    data: param,
  });
}
export function getAreaList(param: params2tList): Promise<APIResponse> {
  return http.request({
    method: "get",
    url: `/area/query`,
    params: {
      keyword: param.keywords,
    },
  });
}
export interface params4saveDraft {
  oid: number;
  prjForm?: number;
  prjType?: string;
  title?: string;
  purpose?: string;
  description?: string;
  coverPic?: string;
  prjTag?: number[];
  targetKlg?: string[];
  status?: number;
}
export function saveDraftAPI(param: params4saveDraft): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: "/tprj/save",
    data: param,
  });
}
export function getVideoSection(sectionId: number): Promise<APIResponse> {
  return http.request({
    method: "get",
    url: `/video/detailContent/${sectionId}`,
  });
}
export function getTextSection(sectionId: number): Promise<APIResponse> {
  return http.request({
    method: "get",
    url: `/twords/detail/${sectionId}`,
  });
}
export function getTextExamSection(sectionId: number): Promise<APIResponse> {
  return http.request({
    method: "get",
    url: `/twords/getExamDetail/${sectionId}`,
  });
}
export function uploadLecture(param: FormData): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: "/video/caption/saveVideoCaption",
    data: param,
  });
}
export function clearLecture(sectionId: number): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: "/video/caption/deleteContent",
    data: {
      sectionId: sectionId,
    },
  });
}
export interface params4addSection {
  prjId: string;
  chapterNum: number;
}
export function addVideoSection(
  param: params4addSection
): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: "/video/saveContent",
    data: param,
  });
}
export function addTextSection(param: params4addSection): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: "/wordSection/addNewWordsSection",
    data: param,
  });
}
export interface params4submitVideoSection {
  lastModified?: string;
  prjId?: string;
  chapterNum: number;
  sectionId: number;
  sectionTitle: string;
}
export function submitVideoSection(
  param: params4submitVideoSection
): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: "/video/updateContent",
    data: param,
  });
}
export interface params4updateVideoLecture {
  beginning: boolean;
  id: number;
  type: number;
  updateManuscript: string;
}
export function updateVideoLecture(
  param: params4updateVideoLecture
): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: "/video/caption/updateSrt",
    data: param,
  });
}
export function deleteVideoSection(secId: number): Promise<APIResponse> {
  return http.request({
    method: "get",
    url: `/video/deleteContent/${secId}`,
  });
}
export function deleteTextSection(secId: number): Promise<APIResponse> {
  return http.request({
    method: "get",
    url: `/twords/delete/${secId}`,
  });
}

// 文稿项目-第二步保存小节（收起 | 已有展开小节时展开其他小节 | 存草稿 | 下一步）
export interface params4submitTextSection {
  projectId: number;
  projectSections: textSection[];
}
export function submitTextSection(
  param: params4submitTextSection
): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: "/twords/save",
    data: param,
  });
}
export interface params4getQuestionList {
  projectId: number;
  sectionId: number;
  questionId?: number;
}
export function deleteQuestion4Text(qId: number): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: "/question/delete",
    data: {
      questionId: qId,
    },
  });
}
export function deleteQuestion4Video(qId: number): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: "/video/caption/deleteQuestion",
    data: {
      questionId: qId,
    },
  });
}
export function deleteQuestion4TextExam(qId: number): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: "/twords/deleteQuestion",
    data: {
      questionId: qId,
    },
  });
}
export function submitProject(projectId: number): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: "/tprj/submitProject",
    data: {
      prjId: projectId,
    },
  });
}
export function getExamContentApi(
  secId: number,
  cntId: number
): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: "/twords/getExamContent",
    data: {
      sectionId: secId,
      contentId: cntId,
    },
  });
}
// 删除视频小节封面
export function removeVideoCoverApi(secId: string): Promise<APIResponse> {
  return http.request({
    method: "post",
    url: `/cos/file/cover/remove`,
    data: {
      chapterId: secId,
    },
  });
}
