<script setup lang="ts">
import { ref, nextTick } from "vue";
import type { prjInfo4table } from "@/utils/type";
import { typeDict } from "@/utils/constant";
import MyButton from "@/views/common/myButton.vue";
import { letMe2Examine, letOthers2Examine } from "@/apis/path/projectExamine";
import { ElMessage } from "element-plus";
import { renderMarkdown } from "@/utils/markdown";
const emits = defineEmits(["update"]);
const isDialogShow = ref();
const tableData = ref<prjInfo4table[]>();
const fromPool = ref(); // 1: my | 2: all
const showDialog = (form: number, examineList: prjInfo4table[]) => {
  // form: 1: my->all | 2: all -> my
  tableData.value = examineList;
  fromPool.value = form;
  isDialogShow.value = true;
};
const handleClose = () => {
  isDialogShow.value = false;
};
const handleSubmit = () => {
  if (fromPool.value == 1) {
    letOthers2Examine(
      tableData.value?.map((p: prjInfo4table) => p.id) as number[]
    )
      .then((res) => {
        if (res.success) {
          ElMessage.success("换人成功");
          emits("update");
          handleClose();
        } else {
          ElMessage.error("换人失败");
        }
      })
      .catch();
  } else if (fromPool.value == 2) {
    letMe2Examine(tableData.value?.map((p: prjInfo4table) => p.id) as number[])
      .then((res) => {
        if (res.success) {
          ElMessage.success("换人成功");
          emits("update");
          handleClose();
        } else {
          ElMessage.error("换人失败");
        }
      })
      .catch();
  }
};
defineExpose({
  showDialog,
});
</script>

<template>
  <el-dialog
    class="examine-dialog"
    v-model="isDialogShow"
    :close-on-click-modal="false"
    width="596"
  >
    <template #header="{ titleId }">
      <div :id="titleId" class="dialog-title">
        {{ fromPool == 1 ? "换人审核" : "我来审核" }}
      </div>
    </template>
    <el-table
      ref="prjTable"
      :data="tableData"
      style="width: 100%"
      empty-text="暂无数据"
    >
      <el-table-column
        class="notFixWidth"
        prop="prname"
        label="项目名称"
        width="215"
      >
        <template #default="scope">
          <span style="width: 100%; justify-content: flex-start">
            <span
              class="ck-content"
              v-html="renderMarkdown(scope.row.prname)"
            ></span>
          </span>
        </template>
      </el-table-column>
      <el-table-column
        prop="prAuthorName"
        label="作者"
        width="118"
      ></el-table-column>
      <el-table-column
        prop="submitTime"
        label="提交日期"
        width="171"
      ></el-table-column>
    </el-table>
    <template #footer>
      <div class="footer">
        <div class="btn-group">
          <my-button type="light" @click="handleClose">关闭</my-button>
          <my-button @click="handleSubmit">确认</my-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.el-table {
  &:deep(.el-table__cell) {
    padding: 0;
  }
  &:deep(.cell) {
    justify-content: center;
    display: flex;
    align-items: center;
    min-height: 46px;
  }
}
.dialog-title {
  font-size: 16px;
  font-family: var(--font-family-text);
  font-weight: 600;
}
.footer {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
  .btn-group {
    display: flex;
    justify-content: space-between;
    width: 280px;
  }
}
</style>
