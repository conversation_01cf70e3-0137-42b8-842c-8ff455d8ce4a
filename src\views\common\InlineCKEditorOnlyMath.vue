<script setup lang="ts">
import { inject, onMounted, ref } from "vue"
// import CKEditor from "@ckeditor/ckeditor5-vue"
// import Editor from "ckeditor5-custom-build"
// const editor = Editor.InlineEditorOnlyMath
import { defineModel, defineProps } from "vue"
const Editor = inject("Editor") as any
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false,
  },
  placeholder: {
    type: String,
    default: '请输入'
  }
})
const editorData = ref()
const editorConfig = {
  placeholder: props.placeholder,
}
const disable = ref()
const getData = () => {
  return editorData.value
}
const model = defineModel()
const setData = (value: string, dis?: boolean) => {
  editorData.value = value
  disable.value = dis
  if (dis == undefined) {
    disable.value = false
  }
}
onMounted(() => {
  model.value += " "
})
defineExpose({
  getData,
  setData,
})
</script>

<template>
  <div class="ckeditor-wrapper">
  <ckeditor
    class="inlineEditor"
    :disabled="props.disabled"
    :editor="Editor.InlineEditorOnlyMath"
    :config="editorConfig"
    v-model="model as string"
  >
  </ckeditor>
</div>
</template>

<style scoped>

.ckeditor-wrapper {
  width: 100%;
  border: 1px solid var(--color-light);
  border-radius: 5px;
  .ck.ck-editor__editable_inline>:last-child {
    margin-bottom: 5px;
  }

  .ck.ck-editor__editable_inline>:first-child {
    margin-top: 5px;
  }

}
</style>

