<script setup lang="ts">
import QuestionList from "@/views/createProject/step3/questionList.vue";
import { inject, onMounted, type Ref, ref, watch, watchEffect, nextTick } from 'vue';
import type { examSectionType, questionType, tagType } from "@/utils/type";
import { examType, examTypeDict, qMode, qType, qTypeDict } from "@/utils/constant";
import ModeTypeSwitcher from "@/views/createProject/step3/modeTypeSwitcher.vue";
import useLineWord from "@/utils/lineWord";
import QuestionDialog from "@/views/createProject/step3/questionDialog.vue";
import { findKeyByValue } from "@/utils/func";
import { ElMessage } from "element-plus";

import { deleteQuestion4TextExam, getExamContentApi } from "@/apis/path/createProject";
import { type params4addQuestion, saveQuestion4textExam } from "@/apis/path/lineWordApis";

const { handleLineWord, updataDom, markWord, unmarkWord, getQuestionList } = useLineWord();
const props = defineProps({
  curProjectId: {
    type: Number,
    required: true
  },
  curSectionId: {
    type: Number,
    required: true
  },
  curContentId: {
    type: Number,
    required: true
  },
  // examContent: {
  //   type: Object as () => examSectionType,
  //   required: true,
  // },
  // questionList: {
  //   type: Array as () => questionType[],
  //   required: true,
  // },
});
const curPrjId = ref();
const curSecId = ref();
const cntId = ref(); // 本组件的静态id
const curCntId = inject('showContentId') as Ref; // 活跃的内容id，由wrapper传过来，在本组件监听变化
// 如果curCntId和cntId没有对上，则不更新数据
const examData = ref<examSectionType>({
  id: -1,
  examAnswer: '',
  examChoices: [''],
  examExplanation: '',
  examTitle: '',
  examType: examType.blank,
});
const questionDialogRef = ref();
const curMode = ref<number>(0); // 0：reading || 1：questioning
const quesList = ref<questionType[]>([]);
const isDialogShowing = ref();
const isOperable = ref();
isOperable.value = !inject('displaying') as boolean;
// 定义需要获取的dom元素
const content = ref();

const handleWord = (el: any) => {
  // 提问模式禁止点击
  if (curMode.value == 1) {
    return;
  }
  // 打开窗口时禁止点击（窗口的关联文本字段是html渲染的，所以会有onclick点击事件）
  if (isDialogShowing.value) {
    return;
  }
  const id = el.getAttribute('data-qid');
  if (id == '') {
    // ElMessage({
    //   type: 'error',
    //   message: '没有qid'
    // });
    return;
  }
  getQuestionList(id).then(questionList => {
    questionDialogRef.value.showDialog(questionList, 2);
  })
};

const refreshExamContent = (newContent: examSectionType) => {
  examData.value = newContent;
  if (curMode.value == 0) {
    // markWord();
    // nextTick操作确保在第一次渲染时能正确的显示效果
    nextTick(() => {
      markWord();// Math初始化

    });
  }
}
const getExamContent = () => {
  getExamContentApi(curSecId.value, cntId.value).then(res => {
    if (res.success) {
      const content: examSectionType = {
        id: res.data.content[0].id,
        examAnswer: res.data.content[0].examAnswer,
        examChoices: res.data.content[0].examChoices,
        examExplanation: res.data.content[0].examExplanation,
        examTitle: res.data.content[0].examTitle,
        examType: examTypeDict[res.data.content[0].examType] as examType,
      }
      refreshExamContent(content);
      const qList = res.data.questionAndAnswers;
      quesList.value = qList?.map(q => {
        return {
          contentId: 0,
          qId: q.question.oid,
          isValid: q.question.valid,
          relatedText: q.question.associatedWords,
          qContent: q.question.keyWords,
          qType: qTypeDict[q.question.questionType], // 是什么 | 为什么
          qMode: q.question.questionNecessity, // 必要 | 参考
          klg: q.answers[0].answerKlgs?.map((i: { klgCode: string, title: string }) => {
            return {
              id: i.klgCode,
              name: i.title,
            }
          }),
          explanation: q.answers[0].answerExplanation,
        }
      })
    }
  })
}

watch(() => props, (newValue, oldValue) => {
  curPrjId.value = newValue.curProjectId;
  curSecId.value = newValue.curSectionId;
  cntId.value = newValue.curContentId;
}, { deep: true, immediate: true });
// 此处用watch会失效但是使用watchEffect会成功是为什么！！！！！
// watch(() => curCntId.value, (newValue, oldValue) => {
//   if (newValue == cntId.value) {
//     getExamContent();
//     console.log('contentttt', content.value);
//   }
// }, { deep: true, immediate: true });

watchEffect(() => {
  if (curCntId.value === cntId.value) {
    getExamContent();
    content.value = document.getElementById(curCntId.value);
  }
});
const handleAddQuestion = (richText: string, text: string) => {
  let ques: questionType = {
    qId: -1,
    isValid: true,
    relatedText: richText,
    qContent: text,
    qType: qType.what,
    qMode: qMode.ness,
    klg: [],
    explanation: '',
  }
  questionDialogRef.value.showDialog([ques], 0); // mode=0：add question
}
const handleOpenDialog = () => isDialogShowing.value = true;
const handleCloseDialog = () => isDialogShowing.value = false;
const handleEditQuestion = () => handleCloseDialog(); // 通信逻辑放到子组件questionList里，本方法只用于示意孙子组件questionDialog已经关闭，可以恢复点击高亮词汇的功能
const handleChangeMode = (newMode: number) => {
  curMode.value = newMode;
  if (curMode.value == 0) {
    markWord(); // 阅读模式
  }
  else {
    unmarkWord(); // 提问模式
  }
}
const handleSubmitQuestion = (newQuestion: questionType) => {
  handleCloseDialog();
  const param: params4addQuestion = {
    prjId: curPrjId.value,
    secId: curSecId.value,
    contentId: cntId.value,
    relatedText: newQuestion.relatedText, // 给后端传的是划词产生的富文本源码
    content: newQuestion.qContent, // 可以编辑的文本内容
    type: findKeyByValue(newQuestion.qType, qTypeDict) as string, // '是什么' | '为什么'
    mode: newQuestion.qMode,
    klgCode: newQuestion.klg?.map((klg: tagType) => klg.id), // 关联知识点
    explanation: newQuestion.explanation, // 说明
  }
  saveQuestion4textExam(param).then(res => {
    if (res.success) {
      quesList.value?.push({
        isValid: true,
        qId: res.data.questionId,
        relatedText: newQuestion.qContent,
        qContent: newQuestion.qContent,
        qType: newQuestion.qType,
        qMode: newQuestion.qMode,
        klg: newQuestion.klg,
        explanation: newQuestion.explanation,
      })
      const content: examSectionType = {
        id: res.data.result.id,
        examAnswer: res.data.result.examAnswer,
        examChoices: res.data.result.examChoices,
        examExplanation: res.data.result.examExplanation,
        examTitle: res.data.result.examTitle,
        examType: examTypeDict[res.data.result.examType] as examType,
      }
      console.log('333debug: getExamContent: ', JSON.stringify(content, null, 2));
      // 刷新题目内容
      refreshExamContent(content);
      ElMessage.success('保存问题成功');
    }
    else {
      ElMessage.error(res.message)
    }
  }).catch();
}
const handleDeleteQuestion = (deleteQuestionId: number) => {
  if (!isOperable.value) {
    console.log('不应该到这里的');
    return;
  }
  deleteQuestion4TextExam(deleteQuestionId).then(res => {
    if (res.success) {
      quesList.value = quesList.value?.filter(q => q.qId != deleteQuestionId);

      const content: examSectionType = {
        id: res.data.speechText.id,
        examAnswer: res.data.speechText.examAnswer,
        examChoices: res.data.speechText.examChoices,
        examExplanation: res.data.speechText.examExplanation,
        examTitle: res.data.speechText.examTitle,
        examType: examTypeDict[res.data.speechText.examType] as examType,
      }
      console.log('44444debug: getExamContent: ', JSON.stringify(content, null, 2));
      ElMessage.success('删除问题成功');
      // TODO: 刷新题目内容
      refreshExamContent(content);
    }
    else {
      ElMessage.error(res.message)
    }
  }).catch(() => { })
}
onMounted(() => {
  window.handleWord = handleWord;
  content.value = document.getElementById(curCntId.value);
  // console.log(curCntId.value, cntId.value);
  // console.log('content11', content.value);

  // 监听content.value的变化，以便重新绑定事件监听器
  watchEffect(() => {
    if (content.value) {
      content.value.addEventListener('mouseup', () => {
        if (curMode.value == 0) {
          return;
        }
        const result = handleLineWord();
        if (result !== '') {
          handleAddQuestion(result, window.getSelection()?.toString());
        }
      });
    }
  });
})

</script>

<template>
  <div class="exam-container">
    <div class="exam-content">
      <div v-if="isOperable" style="margin-bottom: 10px;">
        <mode-type-switcher :mode="curMode" @changeMode="handleChangeMode">
          <template v-slot:mode0>
            阅读模式
          </template>
          <template v-slot:mode1>
            提问模式
          </template>
        </mode-type-switcher>
      </div>
      <div :id=cntId>
        <div class="stem-container">
          <!--          <span class="flatter-text text-wrapper">{{examData.examTitle}}</span>-->
          <span class="flatter-text text-wrapper" v-html="examData.examTitle"></span>
        </div>
        <div class="ans-container">
          <template v-if="examData.examType == examType.select">
            <div class="choiceList">
              <span class="choice-wrapper" v-for="(c, idx) in examData.examChoices" :key="idx">

                <!--              <span class="flatter-text text-wrapper">{{examData.examChoices[idx]}}</span>-->
                <span class="flatter-text text-wrapper" v-html="examData.examChoices[idx]"></span>
              </span>
            </div>
            <!--            <span class="higher-text text-wrapper">{{examData.examExplanation}}</span>-->
            <span class="higher-text text-wrapper" v-html="examData.examExplanation"></span>
          </template>
          <template v-if="examData.examType == examType.blank">
            <div class="blank-wrapper">
              <!--              <span class="flatter-text text-wrapper">{{examData.examAnswer}}</span>-->
              <span class="flatter-text text-wrapper" v-html="examData.examAnswer"></span>
              <!--              <span class="higher-text text-wrapper">{{examData.examExplanation}}</span>-->
              <span class="higher-text text-wrapper" v-html="examData.examExplanation"></span>
            </div>
          </template>
          <template v-if="examData.examType == examType.judge">
            <el-radio-group disabled class="radio-bar" v-model="examData.examAnswer">
              <el-radio label="1">√</el-radio>
              <el-radio label="0">×</el-radio>
            </el-radio-group>
            <!--            <span class="higher-text text-wrapper">{{examData.examExplanation}}</span>-->
            <span class="higher-text text-wrapper" v-html="examData.examExplanation"></span>
          </template>
          <template v-if="examData.examType == examType.qa">
            <!--            <span class="higher-text text-wrapper">{{examData.examAnswer}}</span>-->
            <span class="higher-text text-wrapper" v-html="examData.examAnswer"></span>
            <!--            <span class="higher-text text-wrapper">{{examData.examExplanation}}</span>-->
            <span class="higher-text text-wrapper" v-html="examData.examExplanation"></span>
          </template>
        </div>
      </div>
    </div>
    <!-- <div class="right-wrapper">
      <question-list @open="handleOpenDialog" @close="handleCloseDialog" @edit="handleEditQuestion"
        @delete="handleDeleteQuestion" :question-list="quesList"></question-list>
    </div> -->
  </div>
  <question-dialog @open="handleOpenDialog" @close="handleCloseDialog" @submit="handleSubmitQuestion"
    ref="questionDialogRef"></question-dialog>
</template>

<style scoped>
:deep(.highLight) {
  color: var(--color-primary);
  cursor: pointer;
}

.exam-container {
  display: flex;
  padding: 22px;
  background-color: var(--color-light);

  .exam-content {
    display: flex;
    width: 100%;
    margin-right: 20px;
    flex-direction: column;

    .text-wrapper {
      width: 100%;
      display: flex;
      background-color: white;
      margin-bottom: 20px;
      border: solid 1px var(--color-boxborder);
      flex-wrap: wrap;
    }

    .flatter-text {
      padding: 0 5px;
      height: 35px;
      line-height: 35px;
    }

    .higher-text {
      padding: 5px;
      height: 110px;
    }

    .stem-container {
      display: flex;
      flex-direction: column;
      align-items: flex-start;

      :deep(.el-input__wrapper) {
        border-radius: 0;
        background-color: white;
      }
    }

    .ans-container {
      display: flex;
      flex-direction: column;
      width: 100%;

      .radio-bar {
        margin-bottom: 16px;
        width: 155px;
        display: flex;
        justify-content: space-between;

        :deep(.el-radio__input.is-checked .el-radio__inner),
        :deep(.el-radio__inner:hover) {
          border-color: var(--color-grey);
          background: white;
        }

        :deep(.el-radio__inner::after) {
          width: 8px;
          height: 8px;
          background: var(--color-grey);
        }

        :deep(.el-radio__input.is-checked + .el-radio__label) {
          color: var(--color-black);
        }
      }

      .choiceList {

        /* 此处的容器高度是否能被撑开，来判断是否需要给这个div设置height */
        .choice-wrapper {
          display: flex;
          flex-direction: column;
          width: 100%;
          align-items: center;

          :deep(.el-checkbox__label) {
            font-weight: 400;
            padding-left: 5px;
          }
        }
      }

      .blank-wrapper {
        display: flex;
        flex-direction: column;
        width: 100%;

        :deep(.el-input__wrapper) {
          padding: 0 10px;
        }
      }
    }
  }

  .right-wrapper {
    width: 465px;
    display: flex;
  }
}
</style>