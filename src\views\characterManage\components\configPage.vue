<!-- <script setup lang="ts">
import { onMounted, ref } from "vue";
import MyButton from "@/views/common/myButton.vue";
import { pageList } from "@/utils/constant";
import { editCharacter, getCharacterDetails, type params2character } from "@/apis/path/characterManage";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";

// const checkedList = ref<string[]>(['checking','recycle','myproject']);
// const checkedList = ref<string[]>(['recycle','myproject']);
const checkedList = ref<string[]>([]);
const crctrId = ref();
const crctrName = ref();
const route = useRoute();
const checkAll = (groupTitle: string) => {
  let res = true;
  const list = pageList.find(p => p.title == groupTitle)?.subPageList;
  if (!list || list.length == 0) {
    return checkedList.value.includes(pageList.find(p => p.title == groupTitle)?.tag as string);
  }
  list?.map(r => {
    if (!checkedList.value.includes(r.tag)) {
      res = false;
    }
  });
  return res;
}
const checkState = (groupTitle: string) => {
  let count = 0;
  const list = pageList.find(p => p.title == groupTitle)?.subPageList;
  list?.map(r => {
    if (checkedList.value.includes(r.tag)) count++;
  });
  return !((count == list?.length) || count == 0);
}
const handleCheckAllChange = (groupTitle: string) => {
  const fullList = pageList.find(p => p.title == groupTitle)?.subPageList.map(r => r.tag);
  if (!fullList || fullList.length == 0) {
    const tag = pageList.find(p => p.title == groupTitle)?.tag as string;
    if (checkedList.value.includes(tag)) {
      checkedList.value = checkedList.value.filter(c => c != tag)
    }
    else checkedList.value.push(tag);
  }
  if (checkState(groupTitle)) {
    //   半选状态 -> 全选状态
    fullList?.map(r => {
      if (!checkedList.value.includes(r)) {
        checkedList.value.push(r);
      }
    })
  }
  else if (checkAll(groupTitle)) {
    //   全选状态 -> 全不选
    checkedList.value = checkedList.value.filter(checkedR => !fullList?.includes(checkedR));
  }
  else {
    //   全不选 -> 全选状态
    fullList?.map(r => {
      if (!checkedList.value.includes(r)) {
        checkedList.value.push(r);
      }
    })
  }
}
const handleCheckedRightChange = (value: string[]) => {
}
const getCrctrDetail = () => {
  getCharacterDetails(crctrId.value).then(res => {
    const rightset = res.data.detail.rightset.replace(/^\*+/, '');
    checkedList.value = rightset.split('**');
    crctrName.value = res.data.detail.name;
  })
}
const handleBack = () => {
  window.close();
}
const handleSubmit = () => {
  const param: params2character = {
    oid: crctrId.value,
    rightset: checkedList.value.join('**'),
  }
  editCharacter(param).then(res => {
    if (res.success) {
      ElMessage.success('修改权限成功');
    }
  })
}
onMounted(() => {
  crctrId.value = route.query.id;
  getCrctrDetail();
})
</script>

<template>
  <div class="config-wrapper">
    <div class="btn-bar">
      <span class="btn-group">
        <my-button @click="handleBack" type="light">返回</my-button>
        <my-button @click="handleSubmit">提交</my-button>
      </span>
    </div>
    <div class="main-container">
      <div class="floor">
        <div class="label">
          角色名称
        </div>
        <div class="right-wrapper" style="height: 35px; line-height: 35px; padding-left: 15px; font-size: 14px;">
          {{ crctrName }}
        </div>
      </div>
      <div class="floor">
        <div class="label">
          菜单权限
        </div>
        <div class="right-wrapper">
          <div class="content-wrapper" v-for="subMenu in pageList" :key="subMenu.title">
            <el-checkbox :indeterminate="checkState(subMenu.title)" :model-value="checkAll(subMenu.title)"
              @change="handleCheckAllChange(subMenu.title)">
              <div class="label">
                {{ subMenu.title }}
              </div>
            </el-checkbox>
            <div class="content-container">
              <el-checkbox-group v-model="checkedList" @change="handleCheckedRightChange">
                <el-checkbox v-for="item in subMenu.subPageList" :key="item.title" :label="item.tag" :value="item.tag">
                  <div class="sub-label">
                    {{ item.title }}
                  </div>
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.config-wrapper {
  font-family: var(--font-family-text);
  background-color: white;
  color: var(--color-black);
  width: var(--width-less);
  display: flex;
  flex-direction: column;
  align-items: center;

  .btn-bar {
    display: flex;
    width: 100%;
    justify-content: flex-start;
    padding: 20px 30px;

    .btn-group {
      width: 270px;
      display: flex;
      justify-content: space-between;
    }

    /* &::after {
      content: '';
      height: 1px;
      width: 100%;
      background-color: var(--color-line);
      position: absolute;
      left: 0;
      bottom: 0;
    } */
  }

  .main-container {
    width: 100%;
    padding: 20px 30px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .floor {
      display: flex;
      flex-direction: row;
      width: 100%;
      margin-bottom: 20px;

      .label {
        font-size: 14px;
        margin-right: 45px;
      }

      .right-wrapper {
        background-color: var(--color-light);
        border: 1px solid var(--color-boxborder);
        width: 840px;

        .content-wrapper {
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          margin-bottom: 20px;
          padding: 20px 30px;

          .label {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 112px;
            height: 35px;
            background-color: white;
          }

          .content-container {
            display: flex;
            flex-direction: row;
            border-radius: 5px;
          }

          /* &::after {
            content: '';
            height: 1px;
            width: calc(100% - 60px);
            background-color: var(--color-line);
            position: absolute;
            bottom: 0;
            left: 30px;
          } */
        }
      }
    }

  }
}
</style> -->

<template></template>