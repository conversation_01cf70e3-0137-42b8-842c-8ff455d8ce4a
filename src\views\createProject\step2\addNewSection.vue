<script setup lang="ts">
import MyButton from "@/views/common/myButton.vue"
import type { params4addSection } from "@/apis/path/createProject"
import { ref, watch } from "vue"
import { addVideoSection, addTextSection } from "@/apis/path/createProject"
import { ElMessage } from "element-plus"
import { prjForm } from "@/utils/constant"

const props = defineProps({
  form: {
    type: String,
    required: true,
  },
  projectId: {
    type: Number,
    required: true,
  },
  listLength: {
    type: Number,
    required: true,
  },
  addNewFlag: {
    type: Boolean,
    required: true,
  },
  curSecId: {
    type: Number,
  }
})
const emit = defineEmits(["sendNewSection","sendToValidContent"])
const prjId = ref()
const listLen = ref()
const curForm = ref()

// props每次传值更新数据
watch(() => props, (newValue, oldValue) => {
    prjId.value = newValue.projectId
    listLen.value = newValue.listLength
    curForm.value = newValue.form

  },
  { deep: true, immediate: true }
)

// 观察addNewFlag，true则添加新节
watch(() => props.addNewFlag, (newValue, oldValue) => {
  if(newValue)
    addNewSec();
})

// 增加新节验证
// 每当点击按钮时，向step2发送一个emit，父组件inject到相应的组件进行表单验证，成功返回到saveSecDone，改变addNewFlag，增加新节
const addNewSecValid = () => {
  // if(props.curSecId === -1 ){
    addNewSec()
  // }else {
  //   emit("sendToValidContent", 1);
  // }
}
// 增加新节
const addNewSec = () => {
  if (props.addNewFlag || props.curSecId === -1) {
    const param = ref<params4addSection>({
      prjId: prjId.value,
      chapterNum: listLen.value,
    }) 
    if (curForm.value == prjForm.video.toString()) {
      // 增加视频小节
      addVideoSection(param.value)
        .then((res) => {
          if (res.success) {
            emit("sendNewSection", res.data.id)
          } else {
            ElMessage.error(res.message)
          }
        })
        .catch()
    } else if (curForm.value == prjForm.text.toString()) {
      // 增加文稿小节
      addTextSection(param.value)
        .then((res) => {
          if (res.success) {
            emit("sendNewSection", res.data.WordSectionId)
          } else {
            ElMessage.error(res.message)
          }
        })
        .catch()
    } else {
      console.log("出现了不属于文稿和视频的form")
    }
  }else{
    ElMessage.info("请填写节内容")
  }
}
</script>

<template>
  <div class="add-section-wrapper">
    <my-button :width="145" :height="40" @click="addNewSecValid">
      + 新增项目节
    </my-button>
  </div>
</template>

<style scoped>
.add-section-wrapper {
  width: 100%;
  margin-bottom: 20px;
  padding: 30px 0;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  text-align: center;
  align-items: center;
  border: 2px dashed var(--color-primary);
}
</style>
