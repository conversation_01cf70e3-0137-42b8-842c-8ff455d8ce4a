<script setup lang="ts">
import MyFlipper from "@/views/common/myFlipper.vue";
import {onMounted, ref} from "vue";
import {getAdministratorsList} from "@/apis/path/examineManage";
import AdministatorDialog from "@/views/examineManage/components/administatorDialog.vue";

const prjTable = ref();
const tableData = ref();
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const administratorDialogRef = ref();

const getAdmnstrsList = () => {
  // 后端不接受分页信息，但是前端有分页器
  getAdministratorsList().then((res) => {
    tableData.value = res.data.list;
    total.value = res.data.total;
  });
}
const handleChangePage = (newPage: number) => {
  currentPage.value = newPage;
  getAdmnstrsList();
}
const handleClickCount = (id: number, title: string) => {
  administratorDialogRef.value.showDialog(id, title);
}
const indexMethod = (index: number) => {
  return (currentPage.value - 1) * pageSize.value + index + 1;
}
onMounted(() => {
  getAdmnstrsList();
})
</script>

<template>
  <div class="main-container">
    <div class="content-container">
      <div class="line"></div>
      <el-table ref="prjTable" :data="tableData" style="width: 100%" empty-text="暂无数据">
        <el-table-column type="index" :index="indexMethod" label="序号" width="72" >
        </el-table-column>
        <el-table-column prop="title" label="角色名称" width="700" align="center">
          <template #default="scope">
              <span style="width: 100%; justify-content: flex-start">
                <span v-html="scope.row.title"></span>
              </span>
          </template>
        </el-table-column>
        <el-table-column prop="number" label="成员数" width="168" >
          <template #default="scope">
            <span @click="handleClickCount(scope.row.id, scope.row.title)" class="operationBtn">{{scope.row.number}}</span>
          </template>
        </el-table-column>
      </el-table>
      <my-flipper @change-page="handleChangePage" :current="currentPage" :page-size="pageSize" :total="total"></my-flipper>
    </div>
  </div>
  <administator-dialog ref="administratorDialogRef" ></administator-dialog>
</template>

<style scoped>
.main-container {
  font-family: var(--font-family-text);
  color: var(--color-black);
  width: var(--width-less);
  /* margin: 20px auto auto auto; */
  .content-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 20px 30px 30px 30px;
    background-color: white;
    .toolbar {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-direction: row;
      padding-bottom: 20px;
      /* &::after {
        content: '';
        height: 1px;
        width: 100%;
        background-color: var(--color-line);
        position: absolute;
        bottom: 0;
      } */
    }
    .el-table {
      &:deep(.el-table__cell) {
        padding: 0;
      }
      &:deep(.cell) {
        justify-content: center;
        display: flex;
        align-items: center;
        min-height: 46px;
      }
    }
    .operationBtn {
      &.midBtn {
        margin: 0px 20px;
      }
      &:hover {
        cursor: pointer;
        color: var(--color-primary);
        font-weight: 600;
        /* text-decoration: underline; */
      }
    }
  }
}
</style>