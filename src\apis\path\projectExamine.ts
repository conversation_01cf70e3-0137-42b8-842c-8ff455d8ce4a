import { http } from '@/apis';
import type {APIResponse} from "@/utils/type";

export interface key2examineList {
    current: number,
    limit: number,
    prAuthorName?: string, //这个功能暂时不支持
    prForm?: string,
    prName?: string,
    prType?: string,
}
export function getMyExamineList(param: key2examineList): Promise<APIResponse>{
    return http.request({
        method: 'post',
        url: '/process/instance/getOne',
        data: param,
    });
}
export function getWaitingExamineList(param: key2examineList): Promise<APIResponse>{
    return http.request({
        method: 'post',
        url: '/process/instance/getAll',
        data: param,
    });
}
export function letOthers2Examine(param: number[]): Promise<APIResponse>{
    return http.request({
        method: 'post',
        url: '/process/instance/exProcess',
        data: param,
    });
}
export function letMe2Examine(param: number[]): Promise<APIResponse> {
    return http.request({
        method: 'post',
        url: '/process/instance/save',
        data: param,
    });
}
export interface params2GetExamInfo {
    current: number,
    limit: number,
    id: number
}
export function getExamineInfo(params: params2GetExamInfo): Promise<APIResponse> {
    return http.request({
        method: 'post',
        url: '/process/instance/getOneAction',
        data: params,
    });
}
export interface params2Examine {
    currActionName: string,
    projectId: number,
    verifyResult: number,
    verifySuggestion: string
}
export function submitExamine(param: params2Examine): Promise<APIResponse> {
    return http.request({
        method: 'post',
        url: '/process/instance/process',
        data: param,
    });
}