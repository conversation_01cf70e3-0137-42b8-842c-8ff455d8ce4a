<script setup lang="ts">
import FormSwitch from "@/views/common/formSwitch2.vue";
// import IconSearch from "@/views/common/iconSearch.vue"
import {
  prjForm,
  prjState,
  prjType,
  typeDict,
  stateDict,
  qWeight,
  formDict,
  conditionState,
} from "@/utils/constant";
import { nextTick, onMounted, ref, watch, watchEffect } from "vue";
import MyButton from "@/views/common/myButton.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Plus, Edit, Search } from "@element-plus/icons-vue";
import { useRouter } from "vue-router";
import { pushData } from "@/utils/func";
import type {
  prjInfo4table,
  answerItem,
  klgType,
  questionItem,
  projectTableData,
} from "@/utils/type";
import { qMode, qType, qTypeDict } from "@/utils/constant";
import {
  getKlgListApi,
  getAreaListApi,
  type key2prjList,
  getProjectListApi,
} from "@/apis/path/projectSearch";
import MyFlipper from "@/views/common/myFlipper.vue";
import { editingPrjStore } from "@/stores/editingPrj";
import { reactive } from "vue";
import { userInfoStore } from "@/stores/userInfo";
import Index from "../index.vue";
import IconSearch from "../common/iconSearch.vue";
import { renderMarkdown } from "@/utils/markdown";
const router = useRouter();
const prjTable = ref();
const curForm = ref("");
const curState = ref(prjState.default.toString());
const curType = ref(prjType.default.toString());
const tableData = ref([]);
const searchKey = ref("");
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const kList = ref<any[]>([]);
const klgValue = ref("");
const globalKlgCode = ref(""); //全局变量存下klgCode
const globalKlgTitle = ref("");
const globalKlgArea = ref<any[]>([]);
// const kList = ref([])
const userInfo = userInfoStore();
const showSearchCard = ref(true);
const showListCard = ref(false);

const multipleSelection = ref<prjInfo4table[]>([]);
const curAnswerKlg = ref<klgType[]>([]);
const curAnswer = reactive<answerItem>({
  answerExplanation: "",
  answerId: -1,
  answerStatus: -1,
  createTime: "",
  keyWords: "",
  klgNumber: 0,
  prjTitle: "",
  prjType: 0,
  questionType: "",
  taskNumber: 0,
  questionDescription: "",
});
const loading = ref(false);
const curQuestion = reactive<questionItem>({
  questionId: -1,
  questionDescription: "",
  associatedWords: "",
  keyWords: "",
  questionType: "是什么",
  questionNecessity: qMode.ness,
  questionWeight: qWeight.open,
  userName: userInfo.getUserInfo().username,
  createTime: "",
  answerNumber: 0,
  questionState: "",
  canDelete: true,
});

const handleSelect = (form: prjForm) => {
  if (form !== prjForm.all) {
    curForm.value = form.toString();
  } else {
    curForm.value = "";
  }
  getPrjList();
};

const handleSearch = (key: string) => {
  searchKey.value = key;
  getPrjList();
};
const getPrjList = () => {
  const param = ref<key2prjList>({
    klgCode: globalKlgCode.value,
    keyword: searchKey.value,
    prjForm: curForm.value,
    // prjForm:currentPage.value,
    status: curState.value,
    current: currentPage.value,
    limit: pageSize.value,
  });
  // console.log("[param]",param.value)
  // TODO: param.prjForm === 3 返回全部项目
  getProjectListApi(param.value)
    .then((res) => {
      if (res.success) {
        //res.data已经获得，但是遍历到tableData有问题
        tableData.value = res.data.records?.map((prj: projectTableData) =>
          pushData(prj)
        );
        total.value = res.data.total;
      } else {
        ElMessage.error(res.message);
      }
    })
    .catch();
};

const ToPreview = (id: number, form: string) => {
  const prjFrom = formDict[form];
  const { href } = router.resolve({
    path: "/home/<USER>",
    query: {
    prjId: id,
      prjForm: prjFrom,
    },
  });
  window.open(href, "_blank");    
};
const ToEdit = (id: number, form: string) => {
  const prjFrom = formDict[form];
  // TODO：添加行为逻辑后，这个地方可能要动态判断跳往步骤x
  const { href } = router.resolve({
    path: "/home/<USER>/step1",
    query: {
      prjId: id,
      prjForm: prjFrom,
    },
  });
  window.open(href, "_blank");
};
const handleChangePage = (newPage: number) => {
  currentPage.value = newPage;
  getPrjList();
};
const handleSelectionChange = (val: prjInfo4table[]) => {
  multipleSelection.value = val;
};
const indexMethod = (index: number) => {
  return (currentPage.value - 1) * pageSize.value + index + 1;
};

const setCur = ($event: any) => {
  curState.value = $event.curState;
  curType.value = $event.curType;
  getPrjList();
};

//渲染item所属领域
const handleGetKlgArea = (klgcode: string) => {
  getAreaListApi(klgcode).then((res) => {
    if (res.success) {
      // console.log(res.data)
      globalKlgArea.value = res.data;
      // console.log(globalKlgArea.value)
    }
  });
};

//渲染标题，渲染项目列表，并跳转到项目列表
const handleGetKlgList = (klgCode: string) => {
  if (klgCode === "") {
    throw new Error("搜索框不能为空");
  }
  handleGetKlgArea(klgCode);
  // console.log("我跳转到结果列表了")
  getPrjList();
  showListCard.value = true;
  showSearchCard.value = false;
};
//全局存下item.code
const handleGetklgCode = (data: any) => {
  globalKlgCode.value = data.klgCode;
  globalKlgTitle.value = data.klgTitle;
  // console.log(globalKlgCode.value)
  // console.log(data)
};
const handleKlgCode = () => {
  // klgCode.value = 1
};
const handleToSearch = () => {
  // console.log('我跳转到搜索页面了')
  showSearchCard.value = true;
  showListCard.value = false;
  // console.log(showSearchCard)
};
// 处理选择klg
const handleSelectKlg = (item: klgType) => {
  // console.log("item", item)
  // console.log("qtype", curQuestion.questionType)
  // if(item.type === 2){
  //   tagClass.shift()
  // }
  // if(item.type === 1){
  //   tagClass.unshift('option-tag1')
  // }
  if (qTypeDict[curQuestion.questionType] === qType.what) {
    if (curAnswerKlg.value.length === 1) {
      ElMessage.error("最多选择1个标签");
      return;
    }
  }
  item.choose = !item.choose;
  // const foundItem = curAnswerKlg.value.find((i) => i.klgCode === item.klgCode)
  // if (foundItem) {
  // 维护klgList
  const index = curAnswerKlg.value.findIndex(
    (i: klgType) => i.code === item.code
  );
  if (index === -1) {
    curAnswerKlg.value.push(item);
  } else {
    curAnswerKlg.value.splice(index, 1);
  }
  // }
};

// 筛选klg选项
const remoteKlgMethod = (query: string) => {
  if (query) {
    loading.value = true;
    getKlgListApi(query).then((res) => {
      if (res.success) {
        loading.value = false;
        kList.value = res.data;
      }
    });
  } else {
    kList.value = [];
  }
};

onMounted(() => {
  // console.log("[调用]")
  // getPrjList()
  // getprjListApi(globalKlgCode.value)
  document.addEventListener("visibilitychange", () => {
    if (!document["hidden"]) {
      getPrjList();
      // console.log('出现');
    } else {
      //隐藏
      // console.log('隐藏');
    }
  });
});
</script>

<template>
  <div id="box" v-show="showSearchCard">
    <div class="main">
      <div id="search">
        <h1>讲解项目查看</h1>
        <div class="container">
          <div style="margin-top: 10px; display: flex; flex-direction: row">
            <el-select
              v-model="klgValue"
              v-if="qTypeDict[curAnswer.questionType] !== qType.what"
              filterable
              :fit-input-width="true"
              remote
              placeholder="请输入知识名称"
              placement="top"
              :remote-method="remoteKlgMethod"
              :loading="loading"
              :remote-show-suffix="true"
              style="width: 600px"
              popper-class="primary"
            >
              <template #label="{ label }">
                <span class="ck-content" v-html="label"></span>
              </template>
              <el-option
                v-for="item in kList"
                :key="item.klgCode"
                :value="item.klgTitle"
                :class="item.choose ? 'highlight' : ''"
                style="width: 100%"
                @click="handleGetklgCode(item)"
              >
                <span>
                  <span class="ck-content" v-html="item.klgTitle"></span>
                </span>
              </el-option>
            </el-select>
            <el-button
              :icon="Search"
              @click="handleGetKlgList(globalKlgCode)"
            />

            <!-- <el-select
                  v-else
                  filterable
                  :fit-input-width="true"
                  suffix-icon="Search"
                  remote
                  reserve-keyword
                  placeholder="请输入知识名称"
                  placement="top"
                  :remote-method="remoteKlgMethod"
                  :remote-show-suffix="true"
                  :loading="loading"
                > -->
            <!-- <el-option
                    v-for="item in kList"
                    :key="item.klgCode"
                    :label="item.klgTitle"
                    :value="item.klgTitle"
                    :class="item.choose ? 'highlight' : ''"
                    style="width: 100%"
                    @click="handleSelectKlg(item)"
                  >
                    <span
                      class="option-tag"
                      :class="`option-tag${item.type}`"
                      >{{ item.type === 2 ? "领域" : "知识" }}
                      </span
                    >
                    <span v-html="item.title"></span>
                  </el-option> -->
            <!-- </el-select> -->
          </div>
          <span>根据知识名称，查看目标知识包含此知识的所有讲解项目。</span>
        </div>
      </div>
    </div>
  </div>
  <div>
    <div class="main-container" v-show="showListCard">
      <div class="content-container">
        <div class="banner">
          <div class="banner-left">
            <p>目标知识</p>
            <div style="display: flex; flex-direction: row">
              <div
                class="ck-content"
                v-html="globalKlgTitle"
                style="
                  color: #333;
                  font-size: 20px;
                  font-family: --font-family-info;
                "
              ></div>
              <div style="display: inline-block; width: 10px"></div>
              <!-- <span style="color: #666;font-size: 19px;font-family: --font-family-info;">所属领域</span> -->
              <div style="display: inline-block; width: 10px"></div>
              <div
                :key="item.index"
                v-for="item in globalKlgArea"
                class="klgArea"
              >
                {{ item }}
                <div style="display: inline-block; width: 10px"></div>
              </div>
            </div>
          </div>
          <div class="banner-right" @click="handleToSearch">返回查询</div>
        </div>
        <form-switch
          :needCurState="true"
          @select="handleSelect"
          @search="handleSearch"
          @cur="setCur($event)"
          placeholder="请输入项目标题"
        ></form-switch>
        <el-table
          ref="prjTable"
          :data="tableData"
          @selection-change="handleSelectionChange"
          style="width: 100%"
          empty-text="暂无数据"
        >
          <el-table-column
            type="index"
            :index="indexMethod"
            label="序号"
            width="75"
          >
          </el-table-column>
          <el-table-column
            class="notFixWidth"
            prop="title"
            label="项目名称"
            width="458"
          >
            <template #default="scope">
              <span style="width: 100%; justify-content: flex-start">
                <span
                  class="prname ck-content"
                  v-html="renderMarkdown(scope.row.title)"
                ></span>
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="prform" label="项目形式" width="100">
          </el-table-column>
          <el-table-column
            prop="creator"
            label="作者"
            width="143"
          ></el-table-column>

          <el-table-column
            prop="status"
            label="状态"
            width="123"
          ></el-table-column>
          <el-table-column
            prop="createTime"
            label="发布时间"
            width="211"
          ></el-table-column>
        </el-table>
        <my-flipper
          @change-page="handleChangePage"
          :current="currentPage"
          :page-size="pageSize"
          :total="total"
        ></my-flipper>
      </div>
    </div>
  </div>
</template>

<style scoped>
#box {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.main {
  width: 1200px;
  height: 765px;
  background-color: #fff;
}
#search {
  width: 100%;
  padding-top: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.container > span {
  font-family: sans-serif;
  color: #aaa;
  font-size: 12px;
  font-style: normal;
}
h1 {
  color: #1661ab;
}
.input {
  padding-top: 30px;
  width: 600px;
  display: flex;
  flex-direction: row;
}

.banner {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 10px 30px 10px 20px;
}
.klgArea {
  font-size: 14px;
  font-family: --font-family-text;
  margin: 0px;
  padding-top: 5px;
  display: inline-block;
}

.banner-right {
  padding-top: 5px;
  height: 30px;
  color: var(--color-primary);
}
.main-container {
  font-family: var(--font-family-text);
  color: var(--color-black);
  width: var(--width-content);
  /* margin: 20px auto auto auto; */

  .content-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 20px 30px 30px 30px;
    background-color: white;

    .toolbar {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-direction: row;
      padding-bottom: 20px;

      .select-group {
        width: 390px;
        display: flex;
        justify-content: space-between;

        .select {
          width: 180px;

          &:deep(.el-input) {
            --el-input-height: 35px;
            line-height: 35px;
          }
        }
      }

      /* &::after {
        content: "";
        height: 1px;
        width: 100%;
        background-color: var(--color-line);
        position: absolute;
        bottom: 0;
      } */
    }

    .el-table {
      &:deep(.el-table__cell) {
        padding: 0;
      }

      &:deep(.cell) {
        justify-content: center;
        display: flex;
        align-items: center;
        height: 55px;
      }
    }

    .operationBlock {
      display: flex;
      width: 76px;
      justify-content: space-between;
      align-items: center;
      flex-direction: row;

      .operationBtn {
        color: var(--color-primary);
        &:hover {
          cursor: pointer;
          color: var(--color-primary);
          text-decoration: underline;
        }
      }

      .operationBtn2 {
        color: #f2f2f2;
      }
    }
  }
}
</style>
