<script setup lang="ts">
import useLineWord from "@/utils/lineWord";
import { inject, nextTick, onMounted, ref, watch } from "vue";
import type {
  lectureType,
  questionType,
  tagType,
  videoContentType,
} from "@/utils/type";
import LectureLine from "@/views/createProject/components/lectureLine.vue";
import QuestionList from "@/views/createProject/step3/questionList.vue";
import QuestionDialog from "@/views/createProject/step3/questionDialog.vue";
import { qMode, qType, qTypeDict } from "@/utils/constant";
import { findKeyByValue } from "@/utils/func";
import { ElMessage } from "element-plus";
import ModeTypeSwitcher from "@/views/createProject/step3/modeTypeSwitcher.vue";
import {
  type params4addQuestion,
  saveQuestion4video,
} from "@/apis/path/lineWordApis";
import {
  deleteQuestion4Video,
  getVideoSection,
} from "@/apis/path/createProject";
import VideoPreview from "@/views/common/videoPreview.vue";

const { handleLineWord, markWord, unmarkWord, getQuestionList } = useLineWord();
const props = defineProps({
  curProjectId: {
    type: Number,
    required: true,
  },
  curSectionId: {
    type: Number,
    required: true,
  },
});
const curPrjId = ref();
const curSecId = ref();
// const curCntId = ref();
const questionDialogRef = ref();
const quesList = ref<questionType[]>([]);
const isDialogShowing = ref(false); // 通过问题列表点开的弹窗是本组件的孙子组件，不好用ref获得显示状态，所以本组件单独维护一个
const curMode = ref<number>(0); // 0：reading || 1：questioning
const videoSectionData = ref<videoContentType>({
  videoCover: {
    echoUrl: "",
    commUrl: "",
  },
  videoKey: "",
  lecture: [],
});
const isOperable = ref();
isOperable.value = !inject("displaying") as boolean;
const getSectionData = () => {
  if (curSecId.value) {
    getVideoSection(curSecId.value).then((res) => {
      const qList = res.data.questionAndAnswer;
      // alert('1')
      quesList.value = qList?.map((q) => {
        return {
          qId: q.question.oid,
          isValid: q.question.valid,
          relatedText: q.question.associatedWords,
          qContent: q.question.keyWords,
          qType: qTypeDict[q.question.questionType], // 是什么 | 为什么
          qMode: q.question.questionNecessity, // 必要 | 参考
          klg: q.answers[0].answerKlgs?.map(
            (i: { klgCode: string; title: string }) => {
              return {
                id: i.klgCode,
                name: i.title,
              };
            }
          ),
          explanation: q.answers[0].answerExplanation,
        };
      });
      videoSectionData.value.lecture = res.data.list[0].speechText?.map((l) => {
        return {
          beginning: l.beginning,
          id: l.id,
          time: l.time,
          content: l.content, // 加过标签的讲稿文本
        };
      });
      videoSectionData.value.videoKey = res.data.list[0].longVideoUrl;
      videoSectionData.value.videoCover = {
        echoUrl: res.data.list[0].longImageUrl,
        commUrl: res.data.list[0].shortImageUrl,
      };
      nextTick(() => markWord());
    });
  }
};
watch(
  () => props,
  (newValue, oldValue) => {
    curSecId.value = newValue.curSectionId;
    curPrjId.value = newValue.curProjectId;
    getSectionData();
  },
  { deep: true, immediate: true }
);
const refreshLectureContent = (newContent: lectureType[]) => {
  if (!isOperable.value) {
    console.log("不应该到这里的");
    return;
  }
  videoSectionData.value.lecture = [...newContent];
  if (curMode.value == 0) {
    nextTick(() => markWord());
  }
};
const handleChangeMode = (newMode: number) => {
  if (!isOperable.value) {
    console.log("不应该到这里的");
    return;
  }
  curMode.value = newMode;
  if (curMode.value == 0) {
    markWord(); // 阅读模式
  } else {
    unmarkWord(); // 提问模式
  }
};
const handleAddQuestion = (richText: string, text: string) => {
  if (!isOperable.value) {
    console.log("不应该到这里的");
    return;
  }
  let ques: questionType = {
    qId: -1,
    isValid: true,
    relatedText: richText,
    qContent: text,
    qType: qType.what, // 是什么 | 为什么
    qMode: qMode.ness, // 必要 | 参考
    klg: [],
    explanation: "",
  };
  questionDialogRef.value.showDialog([ques], 0); // mode=0：add question
};
const handleWord = (el: any) => {
  // 提问模式禁止点击
  if (curMode.value == 1) {
    return;
  }
  // 打开窗口时禁止点击（窗口的关联文本字段是html渲染的，所以会有onclick点击事件）
  if (isDialogShowing.value) {
    return;
  }
  const id = el.getAttribute("data-qid");
  if (id == "") {
    // ElMessage({
    //   type: 'error',
    //   message: '没有qid'
    // });
    return;
  }
  getQuestionList(id).then((questionList) => {
    questionDialogRef.value.showDialog(questionList, 2);
  });
};
const handleOpenDialog = () => (isDialogShowing.value = true);
const handleCloseDialog = () => (isDialogShowing.value = false);
const handleSubmitQuestion = (newQuestion: questionType) => {
  // alert('a')
  if (!isOperable.value) {
    console.log("不应该到这里的");
    return;
  }
  handleCloseDialog();
  const param: params4addQuestion = {
    prjId: curPrjId.value,
    secId: curSecId.value,
    contentId: -1,
    relatedText: newQuestion.relatedText, // 给后端传的是划词产生的富文本源码
    content: newQuestion.qContent, // 可以编辑的文本内容
    type: findKeyByValue(newQuestion.qType, qTypeDict) as string, // '是什么' | '为什么'
    mode: newQuestion.qMode,
    klgCode: newQuestion.klg?.map((klg: tagType) => klg.id), // 关联知识点
    explanation: newQuestion.explanation, // 说明
  };
  saveQuestion4video(param)
    .then((res) => {
      if (res.success) {
        quesList.value?.push({
          isValid: true,
          qId: res.data.questionId,
          relatedText: newQuestion.relatedText,
          qContent: newQuestion.qContent,
          qType: newQuestion.qType,
          qMode: newQuestion.qMode,
          klg: newQuestion.klg,
          explanation: newQuestion.explanation,
        });
        refreshLectureContent(
          res.data.captionList?.map((c) => {
            return {
              beginning: c.beginning,
              id: c.id,
              time: c.time,
              content: c.processedCaption, // 加过标签的讲稿文本
            };
          })
        );
        ElMessage.success("保存问题成功");
      } else {
        ElMessage.error(res.message);
      }
    })
    .catch();
};
const handleDeleteQuestion = (deleteQuestionId: number) => {
  if (!isOperable.value) {
    console.log("不应该到这里的");
    return;
  }
  deleteQuestion4Video(deleteQuestionId)
    .then((res) => {
      if (res.success) {
        quesList.value = quesList.value?.filter(
          (q) => q.qId != deleteQuestionId
        );
        ElMessage.success("删除问题成功");
        refreshLectureContent(
          res.data.speechText?.map((l) => {
            return {
              beginning: l.beginning,
              id: l.id,
              time: l.time,
              content: l.processedCaption, // 加过标签的讲稿文本
            };
          })
        );
      } else {
        ElMessage.error(res.message);
      }
    })
    .catch(() => {});
};
const handleEditQuestion = () => {
  if (!isOperable.value) {
    console.log("不应该到这里的");
    return;
  }
  // 通信逻辑放到子组件questionList里，本方法只用于示意孙子组件questionDialog已经关闭，可以恢复点击高亮词汇的功能
  handleCloseDialog();
};
onMounted(() => {
  window.handleWord = handleWord;
  const content = document.getElementById("lectureContent");
  content!.addEventListener("mouseup", () => {
    if (curMode.value == 0) {
      return;
    }
    const result = handleLineWord();
    if (result != "") {
      handleAddQuestion(result, window.getSelection()!.toString());
    }
  });
});
</script>

<template>
  <div class="video-question-wrapper">
    <div v-if="!isOperable" class="preview-wrapper">
      <video-preview
        style="margin-bottom: 20px"
        :project-id="curPrjId"
        :cover="videoSectionData.videoCover"
        :video="videoSectionData.videoKey"
      ></video-preview>
    </div>
    <div class="question-wrapper">
      <div class="lecture-container">
        <div v-if="isOperable" style="margin-bottom: 10px">
          <mode-type-switcher :mode="curMode" @changeMode="handleChangeMode">
            <template v-slot:mode0> 阅读模式 </template>
            <template v-slot:mode1> 提问模式 </template>
          </mode-type-switcher>
        </div>
        <div id="lectureContent">
          <lecture-line
            v-for="(lec, idx) in videoSectionData.lecture"
            :key="lec.id"
            :lecture="lec"
            :index="idx"
            :step="3"
          ></lecture-line>
        </div>
      </div>
      <!-- <div class="question-container">
        <question-list @open="handleOpenDialog" @close="handleCloseDialog" @edit="handleEditQuestion" @delete="handleDeleteQuestion" :question-list="quesList"></question-list>
      </div> -->
    </div>
    <div v-if="isOperable" class="preview-wrapper">
      <video-preview
        style="margin-bottom: 30px"
        :project-id="curPrjId"
        :cover="videoSectionData.videoCover"
        :video="videoSectionData.videoKey"
      ></video-preview>
    </div>
  </div>
  <question-dialog
    @open="handleOpenDialog"
    @close="handleCloseDialog"
    @submit="handleSubmitQuestion"
    ref="questionDialogRef"
  ></question-dialog>
</template>

<style scoped>
:deep(.highLight) {
  color: var(--color-primary);
  cursor: pointer;
}
:deep(.highLight:hover) {
  font-weight: 700;
}
.video-question-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  .question-wrapper {
    display: flex;
    flex-direction: row;
    .lecture-container {
      width: 100%;
    }
    .question-container {
      display: flex;
      width: 100%;
    }
  }
  .preview-wrapper {
  }
}


</style>
