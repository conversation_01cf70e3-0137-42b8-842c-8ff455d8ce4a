<script setup lang="ts">
import { nextTick, provide, ref, watch } from "vue";
import type { examSectionType, questionType2sort4exam } from "@/utils/type";
;
import useLineWord from "@/utils/lineWord";
import { examTypeDict, qTypeDict } from "@/utils/constant";
import ExamLine from "@/views/createProject/components/examLine.vue";
import { findKeyByValue } from "@/utils/func";
import ExamQuestion from "@/views/createProject/step3/examQuestion.vue";
import { getTextExamSection, getTextSection } from "@/apis/path/createProject";

const { markWord } = useLineWord();
const props = defineProps({
  curProjectId: {
    type: Number,
    required: true
  },
  curSectionId: {
    type: Number,
    required: true
  },
})
const curPrjId = ref();
const curSecId = ref();
const curCntId = ref();
provide('showContentId', curCntId);
const quesList = ref<questionType2sort4exam[]>([]);
const examContentList = ref<examSectionType[]>([]);
const getSectionData = () => {
  if (curSecId.value) {
    getTextExamSection(curSecId.value).then(res => {
      if (res.success) {
        curCntId.value = res.data.examDetail[0].id;
        examContentList.value = res.data.examDetail?.map(e => {
          return {
            id: e.id,
            examTitle: e.examTitle,
            examType: examTypeDict[e.examType],
          }
        })
        // 将这个操作移到子组件中
        // nextTick(() => markWord());
      }
    })
    // getTextSection(curSecId.value).then(res => {
    //   if(res.success) {
    //     const textSection = res.data.wordsContent.projectSections[0];
    //     const eList = textSection.sectionContent;
    //     curCntId.value = eList[0].id;
    //     examContentList.value = eList?.map(e => {
    //       // const processedJson = e.processedContent;
    //       // const obj = JSON.parse(processedJson);
    //       // console.log(JSON.stringify(obj, null, 2));
    //       return {
    //         id: e.id,
    //         // examAnswer: e.examAnswer,
    //         // examChoices: e.examChoices,
    //         // examExplanation: e.examExplanation,
    //         examTitle: e.examTitle,
    //         examType: examTypeDict[e.examType],
    //       }
    //     })
    //     console.log('get examContentList: ' + JSON.stringify(examContentList.value, null ,2));
    //     // const qList = res.data.wordsContent.questionAndAnswers;
    //     // quesList.value = qList?.map(q => {
    //     //   return {
    //     //     // TODO: 问题体应该有其父的内容id
    //     //     contentId: 0,
    //     //     qId: q.question.oid,
    //     //     isValid: q.question.valid,
    //     //     relatedText: q.question.associatedWords,
    //     //     qContent: q.question.keyWords,
    //     //     qType: qTypeDict[q.question.questionType], // 是什么 | 为什么
    //     //     qMode: q.question.questionNecessity, // 必要 | 参考
    //     //     klg: q.answers[0].answerKlgs?.map((i: {klgCode: string, title: string}) => {
    //     //       return {
    //     //         id: i.klgCode,
    //     //         name: i.title,
    //     //       }
    //     //     }),
    //     //     explanation: q.answers[0].answerExplanation,
    //     //   }
    //     // })
    //     nextTick(() => markWord());
    //   }
    // })
  }
}
// const sortQuestion = (contentId: number) => {
//   return quesList.value.filter((q: questionType2sort4exam) => q.contentId = contentId)
// }
watch(() => props, (newValue, oldValue) => {
  curSecId.value = newValue.curSectionId;
  curPrjId.value = newValue.curProjectId;
  getSectionData();
}, { deep: true, immediate: true });
</script>

<template>
  <div class="exam-wrapper">
    <el-collapse v-model="curCntId" accordion>
      <el-collapse-item v-for="(e, idx) in examContentList" :name="e.id" :key="e.id">
        <template #title>
          <exam-line>
            <template v-slot:index>
              {{ idx + 1 }}
            </template>
            <template v-slot:type>
              {{ findKeyByValue(e.examType, examTypeDict) }}
            </template>
            <template v-slot:content>
              <!--              <span class="exam-title-container">-->
              <!--                {{e.examTitle}}-->
              <!--              </span>-->
              <span class="exam-title-container" v-html="e.examTitle">
              </span>
            </template>
          </exam-line>
        </template>
        <exam-question :cur-project-id="curPrjId" :cur-section-id="curSecId" :cur-content-id="e.id"></exam-question>
        <!--        <exam-question :cur-project-id="curPrjId" :cur-section-id="curSecId" :examContent="e" :question-list="sortQuestion(e.id)"></exam-question>-->
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<style scoped>
.exam-wrapper {
  display: flex;
  width: 100%;

  .el-collapse {
    width: 100%;
  }

  :deep(.el-collapse-item__header) {
    height: 100%;
    /*margin-bottom: 10px;*/
  }

  .collapse-title {
    display: flex;
    width: 100%;
    justify-content: space-between;
    height: 35px;
    line-height: 35px;
    /*background-color: var(--color-primary);*/
    /*color: white;*/
    padding: 0 10px;
    cursor: default;

    .btns {
      display: flex;
      width: 88px;
      justify-content: space-between;

      .b {
        cursor: pointer;
      }
    }
  }

  :deep(.el-collapse-item__arrow) {
    display: none;
  }
}
</style>