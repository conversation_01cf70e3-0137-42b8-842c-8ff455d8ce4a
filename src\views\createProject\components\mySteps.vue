<script setup lang="ts">
import {computed, ref, watch} from "vue";

const props = defineProps({
  action: {
    type: Number,
    required: false,
  },
  form: {
    type: String,
    required: true,
  },
  isShowing: {
    type: Boolean,
    required: false,
    default: false,
  }
});
const curForm = ref<string>();
const curAction = ref<number>();
const isStep = ref<boolean>();
const steps = ref([
  {title: '创建项目信息'},
  {title: '添加项目内容'},
])
watch(() => props, (newValue, oldValue) => {
  curForm.value = newValue.form;
  curAction.value = newValue.action;
  isStep.value = newValue.isShowing;
},{deep:true, immediate:true})
</script>

<template>
  <div class="container">
    <!-- <span class="form" v-if="curForm == '1'">
      <img src="@/assets/images/create/u3454.svg">
      视频项目
    </span>
    <span class="form" v-else-if="curForm == '2'">
      <img src="@/assets/images/create/u3462.svg">
      文稿项目
    </span> -->
    
    <el-steps v-if="!isStep" class="steps" :active="curAction + 0.5" finish-status="success" align-center>
      <!-- curAction+0.5 为控制连接线是否激活 -->
      <!-- 
        <el-step title="创建项目信息" />
        <el-step title="添加项目内容" /> 
      -->
      <el-step
      v-for="(step, index) in steps"
      :key = "index"
      :title = "step.title"
      :status = "index == curAction? 'process': ''"
      ></el-step>
    </el-steps>
  </div>
</template>

<style scoped>
.container {
  width: 100%;
  height: 56px;
  display: flex;
  flex-direction: row;
  align-items: center;
  .form {
    padding-left: 30px;
    margin-right: 322px;
    font-size: 16px;
    font-weight: 600;
    color: var(--color-primary);
  }
  .steps {
    margin: 0 auto;
    margin-top: 10px;
    width: 413px;
    --el-color-primary: var(--color-black);
    --el-text-color-placeholder: var(--color-group-background);
    :deep(*) {
      font-family: var(--font-family-text);
      font-weight: 400;
    }
    :deep(.el-step__icon) {
      border: 1px solid;
    }
    :deep(.el-step__title) {
      margin: 6px 0;
      line-height: 20px;
    }
    :deep(.el-step__head.is-success) {
      color: var(--color-black);
    }
    :deep(.el-step__title.is-success) {
      color: var(--color-black)
    }
    :deep(.el-step__line) {
      border-color: var(--color-black)
    }
  }
}
</style>