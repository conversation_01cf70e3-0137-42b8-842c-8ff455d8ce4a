<script setup lang="ts">
import { ref, onMounted } from "vue";
import { getCharacterList } from "@/apis/path/characterManage";
import type { characterObj, administratorObj, userObj } from "@/utils/type";
import { ElMessage } from "element-plus";
import { getAdministratorsList } from "@/apis/path/examineManage";
import MyButton from "@/views/common/myButton.vue";

const emit = defineEmits(['submit']);
const isConfigDialogShow = ref(false);
const userId = ref(0);
const userShowName = ref('');
const characterList = ref();
const examineList = ref();
const curCharacter = ref();
const curExamine = ref('');
const showDialog = (id: number, showname: string, role: string, authority: string) => {
  isConfigDialogShow.value = true;
  userId.value = id;
  userShowName.value = showname;
  curCharacter.value = role;
  curExamine.value = authority;
  // console.log('show', userId.value, userShowName.value, curCharacter.value, curExamine.value);
}
const handleSubmit = () => {
  if (!curCharacter.value) {
    ElMessage.warning('请选择角色');
    return;
  }
  if (!curExamine.value) {
    ElMessage.warning('请选择审批权限');
    return;
  }
  const param = ref<userObj>({
    roleId: curCharacter.value,
    approvalAuthority: curExamine.value,
    oid: userId.value,
  });
  emit('submit', param.value);
  handleClose();
}
const handleClose = () => {
  isConfigDialogShow.value = false;
}
onMounted(() => {
  getCharacterList().then(res => {
    if (res.success) {
      characterList.value = res.data.list
    }
    else {
      ElMessage.error('加载角色列表失败');
    }
  }).catch();
  getAdministratorsList().then(res => {
    if (res.success) {
      examineList.value = res.data.list
    }
    else {
      ElMessage.error('加载审批权限列表失败');
    }
  }).catch();
});
defineExpose({
  showDialog
});
</script>

<template>
  <el-dialog :close-on-click-modal="false" v-model="isConfigDialogShow" width="596">
    <template #header="{ titleId }">
      <div :id="titleId" class="title">
        配置权限
      </div>
    </template>
    <div class="content-container">
      <div class="content-line">
        <span style="white-space: nowrap;">用户账户</span>
        <span class="content">{{ userShowName }}</span>
      </div>
      <div class="content-line">
        <span style="white-space: nowrap;">选择角色</span>
        <el-select v-model="curCharacter" placeholder="请选择角色" class="select" popper-class="primary">
          <el-option v-for="item in characterList" :key="item.id" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
      </div>
      <div class="content-line">
        <span style="white-space: nowrap;">审批权限</span>
        <el-select v-model="curExamine" placeholder="请选择权限" class="select" popper-class="primary">
          <el-option v-for="item in examineList" :key="item.id" :label="item.title" :value="item.title">
          </el-option>
        </el-select>
      </div>
    </div>
    <template #footer>
      <div class="footer">
        <div class="btn-group">
          <my-button type="light" @click="handleClose">关闭</my-button>
          <my-button @click="handleSubmit">确认</my-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
/* el-dialog的渲染是通过teleport直接挂到body下边，在这里写样式不生效，所以把样式写到全局(App.vue)了 */
.title {
  font-size: 16px;
  font-family: var(--font-family-text);
}

.content-container {
  font-family: var(--font-family-text);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .content-line {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 35px;
    margin-bottom: 20px;

    .content {
      width: 420px;
      height: 35px;
      margin-left: 35px;
      display: flex;
      align-items: center;
      padding: 0px 10px;
      background-color: var(--color-light);
      border: 1px solid rgb(220, 223, 230);
      border-radius: 2px;
    }

    .select {
      margin-left: 35px;
      --el-color-primary: var(--el-border-color);
      width: 420px;

      &:deep(.el-input) {
        width: 420px;
        --el-input-height: 35px;
        line-height: 35px;
      }
    }
  }
}

.footer {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-top: 20px;

  .btn-group {
    display: flex;
    justify-content: space-between;
    width: 280px;
  }
}
</style>