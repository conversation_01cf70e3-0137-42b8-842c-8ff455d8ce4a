<template>
  <div>
    <template v-for="(block, index) in processedBlocks" :key="index">
      <div v-if="block.type === 'text'" v-html="block.content"></div>
      <CodeBlock v-else :code="block.code" :classes="block.classes" />
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import CodeBlock from './CodeBlock.vue';
const props = defineProps({
  content: String
});

const processedBlocks = ref<any>([]);

const func = (value: string) => {
  const blocks = value.split(/(<pre><code.*?>[\s\S]*?<\/code><\/pre>)/g);

  return blocks.filter(Boolean).map((block) => {
    if (block.startsWith('<pre><code')) {
      const match = block.match(/<pre><code(?: class="([^"]*)")?>[\n\r]?([\s\S]+?)<\/code><\/pre>/);
      // console.log('match', match);

      if (!match) return { type: 'text', content: block };
      return {
        type: 'code',
        classes: match[1], // 统一转为小写
        code: match[2].trim() // 去除首尾空白
      };
    }

    return { type: 'text', content: block };
  });
};

watch(
  () => props.content,
  (newVal) => {
    if (newVal) {
      processedBlocks.value = func(newVal);
      // console.log('processedBlocks', processedBlocks.value);
    }
  },
  {
    immediate: true
  }
);
</script>
