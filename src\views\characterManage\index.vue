<script setup lang="ts">
import MyFlipper from "@/views/common/myFlipper.vue";
import {onMounted, ref} from 'vue';
import {getCharacterListPage, type params2character, addCharacter, editCharacter, getCharacterDetails, deleteCharacter} from "@/apis/path/characterManage";
import {ElMessage} from "element-plus";
import {stateDict, typeDict} from "@/utils/constant";
import MyButton from "@/views/common/myButton.vue";
import CharacterDialog from "@/views/characterManage/components/characterDialog.vue";
import {useRouter} from "vue-router";

const router = useRouter();
const prjTable = ref();
const tableData = ref();
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const characterRef = ref();
const getCrctrList = () => {
  const params = {
    current: currentPage.value,
    limit: pageSize.value,
  }
  // 后端不接收分页，但是前端有分页器
  getCharacterListPage(currentPage.value, pageSize.value).then((res) => {
    tableData.value = res.data.list;
    total.value = res.data.total;
  });
}
const toConfig = (id: number) => {
  const {href} = router.resolve({
    path: '/home/<USER>',
    query: {
      id: id,
    }
  })
  window.open(href, '_blank');
}
const handleAddCharacter = () => {
  characterRef.value.showDialog();
}
const handleEditCharacter = (id: number, name: string) => {
  characterRef.value.showDialog(id, name);
}
const handleDeleteCharacter = (id: number) => {
  deleteCharacter(id).then((res) => {
    if (res.success) {
      ('删除成功');
      getCrctrList();
    }
    else {
      ElMessage.error('删除失败');
    }
  }).catch();
}
const addCrctr = (param: params2character) => {
  addCharacter(param).then((res) => {
    if (res.success) {
      ElMessage.success('添加成功');
      getCrctrList();
    } else {
      ElMessage.error('添加失败');
    }
  }).catch();
}
const editCrctr = (param: params2character) => {
  editCharacter(param).then((res) => {
    if (res.success) {
      ElMessage.success('编辑成功');
      getCrctrList();
    } else {
      ElMessage.error('编辑失败');
    }
  }).catch();
}
const handleChangePage = (newPage: number) => {
  currentPage.value = newPage;
  getCharacterListPage(currentPage.value, pageSize.value);
}
const indexMethod = (index: number) => {
  return (currentPage.value - 1) * pageSize.value + index + 1;
}
onMounted(() => {
  getCrctrList();
})
</script>

<template>
  <div class="main-container">
    <div class="content-container">
      <div class="toolbar">
        <my-button type="primary" @click="handleAddCharacter">新增角色</my-button>
      </div>
      <div class="line"></div>
      <el-table ref="prjTable" :data="tableData" style="width: 100%" empty-text="暂无数据">
        <el-table-column type="index" :index="indexMethod" label="序号" width="55" >
        </el-table-column>
        <el-table-column prop="name" label="角色名称" width="481" align="center">
          <template #default="scope">
            <span style="width: 100%; justify-content: flex-start">
              <span class="ck-content" v-html="scope.row.name"></span>
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="count" label="成员数" width="117" >
        </el-table-column>
        <el-table-column label="操作" width="285">
          <template #default="scope">
            <span @click="handleEditCharacter(scope.row.id, scope.row.name)" class="operationBtn">修改</span>
            <span @click="toConfig(scope.row.id)" class="operationBtn midBtn">配置页面</span>
            <span @click="handleDeleteCharacter(scope.row.id)" class="operationBtn">删除</span>
          </template>
        </el-table-column>
      </el-table>
      <my-flipper @change-page="handleChangePage" :current="currentPage" :page-size="pageSize" :total="total"></my-flipper>
    </div>
  </div>
  <character-dialog ref="characterRef" @submitAdd="addCrctr" @submitEdit="editCrctr"></character-dialog>
</template>

<style scoped>
.main-container {
  font-family: var(--font-family-text);
  color: var(--color-black);
  width: var(--width-less);
  .content-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 5px 30px 30px 30px;
    background-color: white;
    .toolbar {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-direction: row;
      padding-bottom: 5px;
      /* &::after {
        content: '';
        height: 1px;
        width: 100%;
        background-color: var(--color-line);
        position: absolute;
        bottom: 0;
      } */
    }
    .el-table {
      &:deep(.el-table__cell) {
        padding: 0;
      }
      &:deep(.cell) {
        justify-content: center;
        display: flex;
        align-items: center;
        min-height: 46px;
      }
    }
    .operationBtn {
      color: var(--color-primary);
      &.midBtn {
        margin: 0px 20px;
      }
      &:hover {
        cursor: pointer;
        color: var(--color-primary);
        font-weight: 600;
        /* text-decoration: underline; */
      }
    }
  }
}
</style>