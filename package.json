{"name": "wellerman_frontend_3.0", "version": "0.0.0", "scripts": {"dev": "vite --host", "build": "run-p build-only", "postbuild": "node scripts/copy-vditor-assets.cjs", "preview": "vite preview --port 4173", "build-only": "vite build", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@iktakahiro/markdown-it-katex": "^4.0.1", "@rollup/plugin-commonjs": "^26.0.1", "axios": "1.6.7", "cos-js-sdk-v5": "^1.7.0", "dom-serializer": "^2.0.0", "element-plus": "^2.9.0", "entities": "^6.0.1", "katex": "^0.16.22", "lodash": "^4.17.21", "markdown-it": "^14.1.0", "mathjax": "^3.2.2", "mathjax-full": "^3.2.2", "mitt": "^3.0.1", "pinia": "^2.0.21", "pinia-plugin-persist": "^1.0.0", "uuid": "^9.0.1", "vditor": "^3.11.0", "video.js": "^8.6.1", "vite-plugin-mkcert": "^1.17.7", "vue": "^3.2.38", "vue-cropper": "^1.1.1", "vue-router": "^4.1.5", "highlight.js": "^11.11.1", "@highlightjs/vue-plugin": "^2.1.0"}, "devDependencies": {"@rushstack/eslint-patch": "^1.1.4", "@types/node": "^16.11.56", "@types/url-parse": "^1.4.11", "@vitejs/plugin-vue": "^4.3.4", "@vue/eslint-config-typescript": "^11.0.0", "@vue/tsconfig": "^0.1.3", "code-inspector-plugin": "^0.20.10", "eslint": "^8.22.0", "eslint-plugin-vue": "^9.3.0", "less": "^4.4.1", "npm-run-all": "^4.1.5", "typescript": "~4.7.4", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.2", "url-parse": "^1.5.10", "vite": "^4.5.3", "vue-tsc": "^0.40.7"}}