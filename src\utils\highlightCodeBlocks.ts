import hljs from 'highlight.js';
export function highlightCodeBlocks(value: string) {
  if (!value) return '';

  // 使用正则表达式匹配 <pre><code> 标签
  const codeBlockRegex = /<pre><code(?:\s+class="([^"]*)")?[^>]*>([\s\S]*?)<\/code><\/pre>/g;

  return value.replace(codeBlockRegex, (match, className, codeContent) => {
    try {
      // 解码 HTML 实体
      const decodedContent = codeContent
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'");

      // 使用 hljs 进行语法高亮
      const highlightResult = hljs.highlightAuto(decodedContent);

      // 构建高亮后的代码块
      const highlightedCode = highlightResult.value;
      const detectedLanguage = highlightResult.language || '';

      // 保持原有的 class 属性，并添加 hljs 相关类
      let classAttr = 'hljs';
      if (className) {
        classAttr = `${className} hljs`;
      }
      if (detectedLanguage) {
        classAttr += ` language-${detectedLanguage}`;
      }

      return `<pre><code class="${classAttr}">${highlightedCode}</code></pre>`;
    } catch (error) {
      console.warn('代码高亮处理失败:', error);
      // 如果处理失败，返回原始内容
      return match;
    }
  });
}
