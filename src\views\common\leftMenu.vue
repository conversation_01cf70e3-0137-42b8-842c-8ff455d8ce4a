<script setup lang="ts">
import myButton from "@/views/common/myButton.vue";
import { useRouter, useRoute } from "vue-router";
import { onMounted, ref, watch } from "vue";
import { userInfoStore } from "@/stores/userInfo";
import { FolderOpened, Folder } from "@element-plus/icons-vue";
import { getLeftMenu } from "@/utils/constant";

const router = useRouter();
const userPermissionList = ref<any[]>();

const getIconComponent = (menu: any) => {
  // 根据 menu 的 icon 属性返回对应的图标组件
  const iconMap = {
    FolderOpened: FolderOpened,
    Folder: Folder,
  };
  return iconMap[menu.icon] || FolderOpened;
};

onMounted(() => {
  userPermissionList.value = getLeftMenu();
});

const handleCreatePrj = () => {
  window.open("/home/<USER>", "_blank");
};

// 路由跳转
const routerPush = (targetRoute: string) => {
  if (targetRoute === undefined) {
    alert("路由跳转失败");
  } else {
    router.push(targetRoute);
  }
};
</script>

<template>
  <div class="leftMenu">
    <my-button
      @click="handleCreatePrj()"
      class="createBtn"
      :height="35"
      :width="120"
    >
      <img src="@/assets/images/lefter/u374.svg" style="margin-right: 10px" />
      <span>创建项目</span>
    </my-button>
    <el-menu :default-active="$route.path" :unique-opened="true">
      <div v-for="menu in userPermissionList" :key="menu.path">
        <el-sub-menu :index="menu.path">
          <template #title>
            <div
              style="display: flex; flex-direction: row; align-items: center"
            >
              <component
                :is="getIconComponent(menu)"
                class="el-icon"
              ></component>
              <span class="el-submenu__title">{{ menu.title }}</span>
            </div>
          </template>
          <div v-for="item in menu.children" :key="item.path">
            <el-menu-item :index="item.path" @click="routerPush(item.path)">{{
              item.title
            }}</el-menu-item>
          </div>
        </el-sub-menu>
      </div>
    </el-menu>
    <div class="footer">© 无尽本源 版权所有</div>
  </div>
</template>

<style scoped>
.leftMenu {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  width: 200px;
  background-color: white;
  /*position: relative;*/
  /*z-index: 3000;*/
  font-family: var(--font-family-text);

  .createBtn {
    margin: 15px 0;
  }

  .el-menu {
    border-right: none;
    padding-left: 10px;
    --el-menu-active-color: var(--color-primary);
    --el-menu-active-background-color: var(--color-light);
    --el-menu-item-font-size: 16px;
    width: 100%;
    overflow-y: auto;
    height: calc(100% - 68px - 70px);
    .el-submenu__title {
      margin-left: 10px;
    }
    .el-menu-item.is-active {
      background-color: #ecf5ff; /* 被激活菜单项的背景色 */
    }
    &:deep(.el-menu-item-group__title) {
      padding: 0;
    }
  }

  .footer {
    position: absolute;
    bottom: 0;
    height: 68px;
    border-top: solid 2px #d8e1e9;
    background-color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    color: var(--color-grey);

    &::after {
      content: "";
      position: absolute;
      top: 0;
      width: 100%;
      height: 1px;
      margin: 0 20px;
      background-color: var(--color-line);
    }
  }
}
.el-menu :deep(.el-sub-menu.is-active > .el-sub-menu__title) {
  color: #1661ab !important;
}
</style>
