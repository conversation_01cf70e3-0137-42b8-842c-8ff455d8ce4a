<script setup lang="ts">

import {ref, watch} from "vue";

const prop = defineProps({
  mode: {
    type: Number,
    required: true
  },
  modeValues: {
    type: Array as () => number[],
    required: false,
    default: () => [0,1]
  }
});
const emit = defineEmits(['changeMode']);
const curMode = ref<number>();
const curModeValues = ref<number[]>();
watch(() => prop, (newValue, oldValue) => {
  curMode.value = newValue.mode;
  curModeValues.value = newValue.modeValues;
},{deep:true, immediate:true});
const handleChangeMode = (newMode: number) => {
  curMode.value = newMode;
  emit('changeMode', curMode.value);
}
</script>

<template>
  <div class="switcher-wrapper">
    <div @click="handleChangeMode(modeValues[0])" class="btn" :class="curMode == modeValues[0] ? 'isSelected' : ''">
      <slot name="mode0"></slot>
    </div>
    <div @click="handleChangeMode(modeValues[1])" class="btn" :class="curMode == modeValues[1] ? 'isSelected' : ''">
      <slot name="mode1"></slot>
    </div>
  </div>
</template>

<style scoped>
.switcher-wrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-evenly;
  width: 280px;
  height: 46px;
  background-color: var(--color-group-background);
  .btn {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 42px;
    width: 137px;
    font-size: 14px;
    font-weight: 600;
    font-family: var(--font-family-info);
    &.isSelected, &:hover {
      cursor: pointer;
      background-color: white;
      color: var(--color-primary);
    }
  }
}
</style>