let keyPressedHandler: (event: any) => void
let autoSaveClock: number | null | undefined = null
const AUTO_SAVE_INTERVAL = 5000 // 30秒

// 按键保存
export const listenSave = (element: any, callback: Function) => {
  console.log("add listen 'CTRL + S' to save draft!")
  const handleKeyDown = function (event) {
    if ((event.ctrlKey || event.metaKey) && event.key === "s") {
      event.preventDefault()
      console.log("Ctrl + S 被按下了！")
      callback()
    }
  }
  // 保存对回调函数的引用，以便在移除时使用
  keyPressedHandler = handleKeyDown
  element.addEventListener("keydown", handleKeyDown)
}

// 移除监听
export const removeListenSave = (element: any) => {
  if (keyPressedHandler) {
    element.removeEventListener("keydown", keyPressedHandler)
    console.log("保存事件监听已移除！")
  }
}
// 未编辑自动保存
export const autoSave = (callback: Function, saveTime?: number) => {
  if (autoSaveClock) {
    clearTimeout(autoSaveClock)
  }
  autoSaveClock = setTimeout(
    () => {
      callback()
    },
    saveTime ? saveTime : AUTO_SAVE_INTERVAL
  )
}
