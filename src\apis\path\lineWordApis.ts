import { http } from '@/apis';
import type {APIResponse, tagType} from "@/utils/type";

// 获取问题内容
export function getWord(questionId: string): Promise<APIResponse> {
    return http.request({
        method: 'get',
        url: `/question/getQuestions/${questionId}`,
    })
}
export interface params4addQuestion {
    prjId: number,
    secId: number,
    contentId: number, // 如果是文稿测评类，小节下更细粒度的单元id
    relatedText: string, // 划词直接产生的关联文本
    content: string, // 可以编辑的文本内容
    type: string, // '是什么' | '为什么'
    mode: number, // 1必要 | 2参考
    klgCode: string[], // 关联知识点
    explanation: string, // 说明
}
export function saveQuestion(param: params4addQuestion): Promise<APIResponse> {
    return http.request({
        method: 'post',
        url: '/question/save',
        data: param,
    })
}
export function saveQuestion4video(param: params4addQuestion): Promise<APIResponse> {
    return http.request({
        method: 'post',
        url: '/video/caption/markWords',
        data: param,
    })
}
export function saveQuestion4textExam(param: params4addQuestion): Promise<APIResponse> {
    return http.request({
        method: 'post',
        url: '/twords/markWords',
        data: param,
    })
}
export interface params4editQuestion{
    // valid?: boolean,
    // prjId: number,
    // chapterId: number,
    // contentId: number, // 如果是文稿测评类，小节下更细粒度的单元id
    qid: number, // 问题id
    associatedWords: string,
    keyWords: string,
    questionType: string, // '是什么' | '为什么'
    questionNecessity: number, // 1 | 2
    answerKlgs: string,
    answerExplanation: string,
}
export function editQuestion(param: params4editQuestion): Promise<APIResponse> {
    return http.request({
        method: 'post',
        url: '/question/edit',
        data: param,
    })
}