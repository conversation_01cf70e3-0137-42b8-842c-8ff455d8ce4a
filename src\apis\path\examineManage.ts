import { http } from '@/apis';
import type {APIResponse} from "@/utils/type";

export function getAdministratorsList(): Promise<APIResponse>{
    return http.request({
        method: 'get',
        url: '/user/getPart',
    });
}
export function getMemberList4Administrator(name: string): Promise<APIResponse> {
    return http.request({
        method: 'get',
        url: `/user/getAdmin/${name}`,
    });
}