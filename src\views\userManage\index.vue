<script setup lang="ts">
import FormSwitch from "@/views/common/formSwitch.vue";
import { onMounted, ref } from 'vue';
import { getUserList, type key2userList, editUserInfo } from "@/apis/path/userManage";
import { ElMessage } from "element-plus";
import MyFlipper from "@/views/common/myFlipper.vue";
import ConfigDialog from "@/views/userManage/components/configDialog.vue";
import type { userObj } from "@/utils/type";
import { decodeData2 } from "@/utils/func";
import type { prjForm } from "@/utils/constant";

const prjTable = ref();
const tableData = ref();
const searchKey = ref('');
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const configDialog = ref();
const handleSearch = (key: prjForm) => {
  searchKey.value = key.toString();
  getUsrList();
}
const getUsrList = () => {
  const param = ref<key2userList>({
    current: currentPage.value,
    limit: pageSize.value,
    keywords: searchKey.value,
  });
  getUserList(param.value)
    .then((res) => {
      if (res.success) {
        tableData.value = res.data.list?.map((data: userObj) => decodeData2(data));
        total.value = res.data.total;
      }
      else {
        ElMessage.error(res.message);
      }
    }).catch()
}
const toConfig = (user: { oid: string, showName: string, roleId: string, approvalAuthority: string }) => {
  // console.log('Config : ', user.oid, user.showName, user.roleId, user.approvalAuthority);
  configDialog.value.showDialog(user.oid, user.showName, user.roleId, user.approvalAuthority);
}
const handleSubmitDialog = (param: userObj) => {
  editUserInfo(param).then((res) => {
    if (res.success) {
      // ElMessage.success(res.message);
      getUsrList();
    }
    else {
      ElMessage.error(res.message);
    }
  }).catch();
}
const handleChangePage = (newPage: number) => {
  currentPage.value = newPage;
  getUsrList();
}
const indexMethod = (index: number) => {
  return (currentPage.value - 1) * pageSize.value + index + 1;
}
onMounted(() => {
  getUsrList();
})
</script>

<template>
  <div class="main-container">
    <form-switch :form-func="false" @search="handleSearch" placeholder="请输入项目标题"></form-switch>
    <div class="content-container">
      <div class="line"></div>
      <el-table ref="prjTable" :data="tableData" style="width: 100%" empty-text="暂无数据">
        <el-table-column type="index" :index="indexMethod" label="序号" width="66">
        </el-table-column>
        <el-table-column prop="showName" label="用户账号" width="134">
        </el-table-column>
        <el-table-column prop="loginTime" label="最近登陆时间" width="170">
        </el-table-column>
        <el-table-column prop="expiryDate" label="账号有效期至" width="164"></el-table-column>
        <el-table-column prop="roleName" label="角色" width="159"></el-table-column>
        <el-table-column prop="approvalAuthority" label="审批权限" width="184"></el-table-column>
        <el-table-column prop="isValid" label="是否有效" width="121">
          <template #default="scope">
            {{ scope.row.isValid ? "有效" : "无效" }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="142">
          <template #default="scope">
            <span @click="toConfig(scope.row)" class="operationBtn">配置权限</span>
          </template>
        </el-table-column>
      </el-table>
      <my-flipper @change-page="handleChangePage" :current="currentPage" :page-size="pageSize"
        :total="total"></my-flipper>
    </div>
  </div>
  <!-- 配置权限弹窗 -->
  <config-dialog ref="configDialog" @submit="handleSubmitDialog"></config-dialog>
</template>

<style scoped>
.main-container {
  font-family: var(--font-family-text);
  color: var(--color-black);
  width: var(--width-content);
  margin: 20px auto auto auto;

  .content-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 20px 30px 30px 30px;
    background-color: white;

    .el-table {
      &:deep(.el-table__cell) {
        padding: 0;
      }

      &:deep(.cell) {
        justify-content: center;
        display: flex;
        align-items: center;
        min-height: 46px;
      }
    }

    .operationBtn {
      color: var(--color-primary);
      &:hover {
        cursor: pointer;
        color: var(--color-primary);
        font-weight: 600;
            /* text-decoration: underline; */
      }
    }
  }
}
</style>